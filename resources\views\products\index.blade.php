@extends('layouts.front')

@section('title', 'Premium Poultry Products - ' . ($siteSettings['company_name'] ?? config('app.name')))

@section('content')
<!-- Page Title -->
<section class="relative bg-gradient-to-r from-primary-red to-secondary-red text-white py-20">
    <div class="absolute inset-0 bg-gradient-to-r from-primary-red/90 to-secondary-red/80"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="flex items-center justify-center mb-6">
            <div class="bg-primary-gold text-primary-red rounded-full p-4 mr-4">
                <i class="fas fa-drumstick-bite text-3xl"></i>
            </div>
            <div class="text-left">
                <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">Our Product Range</h3>
                <h1 class="text-4xl md:text-6xl font-bold font-secondary">Premium Poultry Products</h1>
            </div>
        </div>
        <p class="text-xl md:text-2xl text-red-100 mb-8 max-w-3xl mx-auto">Discover our comprehensive range of halal-certified poultry products, processed to international standards and ready for export worldwide.</p>

        <!-- Certification Badges -->
        <div class="flex items-center justify-center space-x-6 mb-8">
            <div class="bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 flex items-center">
                <i class="fas fa-certificate text-primary-gold mr-2"></i>
                <span class="text-sm font-semibold">Halal Certified</span>
            </div>
            <div class="bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 flex items-center">
                <i class="fas fa-shield-alt text-primary-gold mr-2"></i>
                <span class="text-sm font-semibold">HACCP Compliant</span>
            </div>
            <div class="bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 flex items-center">
                <i class="fas fa-globe text-primary-gold mr-2"></i>
                <span class="text-sm font-semibold">Export Quality</span>
            </div>
        </div>

        <nav class="flex justify-center" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('home') }}" class="text-red-200 hover:text-white transition-colors">
                        <i class="fas fa-home mr-2"></i> Home
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-red-300 mx-2"></i>
                        <span class="text-white font-medium">Products</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>
</section>
<!-- End Page Title -->

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
    <div class="flex flex-col lg:flex-row gap-12">
        <!-- Sidebar with categories -->
        <div class="w-full lg:w-1/4">
            <!-- Search Widget -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                <div class="flex items-center mb-6">
                    <div class="bg-primary-red text-white rounded-full p-2 mr-3">
                        <i class="fas fa-search text-lg"></i>
                    </div>
                    <h3 class="text-xl font-bold font-secondary text-text-dark">Search Products</h3>
                </div>

                <form action="{{ route('products.index') }}" method="GET">
                    @if(request('category'))
                        <input type="hidden" name="category" value="{{ request('category') }}">
                    @endif

                    <div class="relative">
                        <input type="text" name="search" value="{{ request('search') }}"
                               class="w-full border-2 border-gray-300 rounded-lg shadow-sm py-3 px-4 pr-12 bg-white focus:border-primary-red focus:ring focus:ring-primary-red focus:ring-opacity-50 transition-all"
                               placeholder="Search poultry products...">

                        <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary-red hover:bg-secondary-red text-white px-3 py-2 rounded-md transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Categories Widget -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center mb-6">
                    <div class="bg-accent-blue text-white rounded-full p-2 mr-3">
                        <i class="fas fa-th-list text-lg"></i>
                    </div>
                    <h3 class="text-xl font-bold font-secondary text-text-dark">Product Categories</h3>
                </div>

                <ul class="space-y-2">
                    <li>
                        <a href="{{ route('products.index') }}"
                           class="flex items-center justify-between py-3 px-4 rounded-lg transition-all {{ !request('category') ? 'bg-primary-red text-white shadow-md' : 'text-gray-700 hover:bg-gray-50 border border-gray-200' }}">
                            <div class="flex items-center">
                                <i class="fas fa-drumstick-bite mr-3 {{ !request('category') ? 'text-primary-gold' : 'text-gray-400' }}"></i>
                                <span class="font-medium">All Products</span>
                            </div>
                            <span class="text-sm {{ !request('category') ? 'text-primary-gold' : 'text-gray-500' }} bg-white/20 px-2 py-1 rounded-full">
                                {{ $products->total() }}
                            </span>
                        </a>
                    </li>

                    @foreach($categories->where('parent_id', null) as $category)
                        <li>
                            <a href="{{ route('products.index', ['category' => $category->slug]) }}"
                               class="flex items-center justify-between py-3 px-4 rounded-lg transition-all {{ request('category') == $category->slug ? 'bg-primary-red text-white shadow-md' : 'text-gray-700 hover:bg-gray-50 border border-gray-200' }}">
                                <div class="flex items-center">
                                    @if($category->slug == 'fresh-poultry')
                                        <i class="fas fa-leaf mr-3 {{ request('category') == $category->slug ? 'text-primary-gold' : 'text-success-green' }}"></i>
                                    @elseif($category->slug == 'processed-parts')
                                        <i class="fas fa-cut mr-3 {{ request('category') == $category->slug ? 'text-primary-gold' : 'text-accent-orange' }}"></i>
                                    @elseif($category->slug == 'premium-cuts')
                                        <i class="fas fa-star mr-3 {{ request('category') == $category->slug ? 'text-primary-gold' : 'text-primary-gold' }}"></i>
                                    @else
                                        <i class="fas fa-tag mr-3 {{ request('category') == $category->slug ? 'text-primary-gold' : 'text-gray-400' }}"></i>
                                    @endif
                                    <span class="font-medium">{{ $category->name }}</span>
                                </div>
                                <span class="text-sm {{ request('category') == $category->slug ? 'text-primary-gold' : 'text-gray-500' }} bg-white/20 px-2 py-1 rounded-full">
                                    {{ $category->products_count }}
                                </span>
                            </a>

                            @if($category->children->count() > 0)
                                <ul class="ml-6 mt-2 space-y-1">
                                    @foreach($category->children as $child)
                                        <li>
                                            <a href="{{ route('products.index', ['category' => $child->slug]) }}"
                                               class="flex items-center justify-between py-2 px-3 rounded-md text-sm transition-all {{ request('category') == $child->slug ? 'bg-accent-blue text-white' : 'text-gray-600 hover:bg-gray-50' }}">
                                                <span>{{ $child->name }}</span>
                                                <span class="text-xs {{ request('category') == $child->slug ? 'text-blue-200' : 'text-gray-400' }} bg-white/20 px-2 py-1 rounded-full">
                                                    {{ $child->products_count }}
                                                </span>
                                            </a>
                                        </li>
                                    @endforeach
                                </ul>
                            @endif
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>

        <!-- Main content -->
        <div class="w-full lg:w-3/4">
            <!-- Header Section -->
            <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
                <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
                    <div>
                        <div class="flex items-center mb-4">
                            <div class="bg-primary-red text-white rounded-full p-2 mr-3">
                                @if(request('category'))
                                    @php
                                        $currentCategory = $categories->where('slug', request('category'))->first();
                                    @endphp
                                    @if($currentCategory && $currentCategory->slug == 'fresh-poultry')
                                        <i class="fas fa-leaf text-lg"></i>
                                    @elseif($currentCategory && $currentCategory->slug == 'processed-parts')
                                        <i class="fas fa-cut text-lg"></i>
                                    @elseif($currentCategory && $currentCategory->slug == 'premium-cuts')
                                        <i class="fas fa-star text-lg"></i>
                                    @else
                                        <i class="fas fa-tag text-lg"></i>
                                    @endif
                                @else
                                    <i class="fas fa-drumstick-bite text-lg"></i>
                                @endif
                            </div>
                            <div>
                                <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">Product Catalog</h3>
                                <h1 class="text-3xl font-bold font-secondary text-text-dark">
                                    @if(request('category'))
                                        {{ $currentCategory ? $currentCategory->name : 'Products' }}
                                    @else
                                        All Poultry Products
                                    @endif
                                </h1>
                            </div>
                        </div>

                        @if(request('search'))
                            <p class="text-lg text-gray-600 mb-2">
                                Search results for "<span class="font-semibold text-primary-red">{{ request('search') }}</span>"
                            </p>
                        @endif

                        <p class="text-gray-600 flex items-center">
                            <i class="fas fa-box mr-2 text-accent-blue"></i>
                            Showing {{ $products->count() }} of {{ $products->total() }} premium poultry products
                        </p>
                    </div>

                    <!-- Sorting and View Options -->
                    <div class="flex items-center gap-4">
                        <div class="flex items-center gap-3">
                            <label for="sort" class="text-sm font-medium text-gray-700">Sort by:</label>
                            <select id="sort" name="sort" class="border-2 border-gray-300 rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red bg-white">
                                <option value="latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>Latest Products</option>
                                <option value="oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>Oldest First</option>
                                <option value="name_asc" {{ request('sort') == 'name_asc' ? 'selected' : '' }}>Name A-Z</option>
                                <option value="name_desc" {{ request('sort') == 'name_desc' ? 'selected' : '' }}>Name Z-A</option>
                                <option value="price_asc" {{ request('sort') == 'price_asc' ? 'selected' : '' }}>Price Low-High</option>
                                <option value="price_desc" {{ request('sort') == 'price_desc' ? 'selected' : '' }}>Price High-Low</option>
                            </select>
                        </div>

                        <div class="flex border-2 border-gray-300 rounded-lg overflow-hidden">
                            <button class="px-4 py-2 bg-primary-red text-white hover:bg-secondary-red transition-colors" title="Grid View">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors" title="List View">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            @if($products->count() > 0)
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($products as $product)
                        <div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-2xl hover:-translate-y-3 group border border-gray-100">
                            <div class="relative overflow-hidden">
                                <a href="{{ route('products.show', $product->slug) }}" class="block">
                                    <div class="w-full h-64 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                        @if($product->primary_image)
                                            <img src="{{ asset('storage/' . $product->primary_image) }}" alt="{{ $product->name }}"
                                                 class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105">
                                        @else
                                            <div class="text-center">
                                                <i class="fas fa-drumstick-bite text-6xl text-gray-400 mb-2"></i>
                                                <p class="text-gray-500 text-sm">Product Image</p>
                                            </div>
                                        @endif
                                    </div>

                                    <!-- Certification Badges -->
                                    <div class="absolute top-4 left-4 flex flex-col gap-2">
                                        <span class="bg-success-green text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                                            <i class="fas fa-certificate mr-1"></i>Halal
                                        </span>
                                        <span class="bg-primary-red text-white px-3 py-1 rounded-full text-xs font-medium shadow-lg">
                                            {{ $product->category->name }}
                                        </span>
                                    </div>

                                    <!-- Export Quality Badge -->
                                    <div class="absolute top-4 right-4">
                                        <span class="bg-accent-blue text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                                            <i class="fas fa-globe mr-1"></i>Export Quality
                                        </span>
                                    </div>

                                    <!-- Hover Overlay -->
                                    <div class="absolute inset-0 bg-gradient-to-t from-primary-red/80 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-center pb-6">
                                        <div class="transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                                            <span class="bg-white text-primary-red px-6 py-3 rounded-full font-bold shadow-xl">
                                                <i class="fas fa-eye mr-2"></i>View Details
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            </div>

                            <div class="p-6">
                                <a href="{{ route('products.show', $product->slug) }}" class="block">
                                    <h3 class="text-xl font-bold font-secondary text-text-dark mb-3 group-hover:text-primary-red transition-colors">
                                        {{ $product->name }}
                                    </h3>

                                    <p class="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
                                        {{ $product->short_description }}
                                    </p>
                                </a>

                                <!-- Product Features -->
                                <div class="flex flex-wrap gap-2 mb-4">
                                    <span class="bg-success-green/10 text-success-green px-2 py-1 rounded-full text-xs font-medium">
                                        <i class="fas fa-certificate mr-1"></i>Halal
                                    </span>
                                    <span class="bg-accent-blue/10 text-accent-blue px-2 py-1 rounded-full text-xs font-medium">
                                        <i class="fas fa-snowflake mr-1"></i>Frozen
                                    </span>
                                    <span class="bg-accent-orange/10 text-accent-orange px-2 py-1 rounded-full text-xs font-medium">
                                        <i class="fas fa-globe mr-1"></i>Export
                                    </span>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex justify-between items-center pt-4 border-t border-gray-100">
                                    <div class="flex gap-2">
                                        <a href="{{ route('products.show', $product->slug) }}"
                                           class="bg-primary-red hover:bg-secondary-red text-white px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 transform hover:scale-105">
                                            <i class="fas fa-eye mr-1"></i>Details
                                        </a>
                                        <button onclick="openQuoteModal()"
                                                class="bg-accent-orange hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 transform hover:scale-105">
                                            <i class="fas fa-envelope mr-1"></i>Quote
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12 flex justify-center">
                    <div class="bg-white rounded-lg shadow-lg p-4">
                        {{ $products->appends(request()->query())->links() }}
                    </div>
                </div>
            @else
                <!-- Empty State -->
                <div class="bg-white rounded-xl shadow-lg p-16 text-center">
                    <div class="max-w-md mx-auto">
                        <div class="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-primary-red to-secondary-red rounded-full flex items-center justify-center">
                            <i class="fas fa-drumstick-bite text-white text-4xl"></i>
                        </div>

                        <h2 class="text-3xl font-bold font-secondary text-text-dark mb-6">No Poultry Products Found</h2>

                        <p class="text-gray-600 mb-8 leading-relaxed text-lg">
                            @if(request('search'))
                                We couldn't find any poultry products matching "<span class="font-semibold text-primary-red">{{ request('search') }}</span>".
                                Try adjusting your search terms or browse our product categories.
                            @elseif(request('category'))
                                There are no products in this category yet. Check back soon or explore other categories.
                            @else
                                There are no products available at the moment. Please check back later.
                            @endif
                        </p>

                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            @if(request('search') || request('category'))
                                <a href="{{ route('products.index') }}"
                                   class="bg-gradient-to-r from-primary-red to-secondary-red hover:from-secondary-red hover:to-primary-red text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105">
                                    <i class="fas fa-grid-3x3 mr-2"></i>View All Products
                                </a>
                            @endif

                            <a href="{{ route('home') }}"
                               class="bg-gray-100 hover:bg-gray-200 text-gray-800 px-6 py-3 rounded-md font-medium transition-colors">
                                <i class="fas fa-home mr-2"></i>Back to Home
                            </a>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sort functionality
    const sortSelect = document.getElementById('sort');

    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            const currentUrl = new URL(window.location.href);
            const params = new URLSearchParams(currentUrl.search);

            // Update or add the sort parameter
            if (this.value && this.value !== 'latest') {
                params.set('sort', this.value);
            } else {
                params.delete('sort');
            }

            // Remove page parameter to start from page 1
            params.delete('page');

            // Redirect to the new URL
            const newUrl = currentUrl.pathname + (params.toString() ? '?' + params.toString() : '');
            window.location.href = newUrl;
        });
    }
});
</script>
@endpush

@endsection
