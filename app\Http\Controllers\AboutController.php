<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Testimonial;
use Illuminate\Http\Request;

class AboutController extends Controller
{
    /**
     * Display the about page
     */
    public function index()
    {
        $branches = Branch::active()->sorted()->get();

        // Get approved testimonials for the about page
        $testimonials = Testimonial::where('status', 'approved')
            ->orderBy('featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        return view('about', compact('branches', 'testimonials'));
    }
}
