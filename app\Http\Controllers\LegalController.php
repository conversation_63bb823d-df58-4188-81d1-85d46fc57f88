<?php

namespace App\Http\Controllers;

use App\Models\SiteSetting;
use Illuminate\Http\Request;

class LegalController extends Controller
{
    /**
     * Display the privacy policy page
     */
    public function privacy()
    {
        $siteSettings = SiteSetting::pluck('value', 'key')->toArray();
        return view('legal.privacy', compact('siteSettings'));
    }

    /**
     * Display the warranty page
     */
    public function warranty()
    {
        $siteSettings = SiteSetting::pluck('value', 'key')->toArray();
        return view('legal.warranty', compact('siteSettings'));
    }

    /**
     * Display the terms of service page
     */
    public function terms()
    {
        $siteSettings = SiteSetting::pluck('value', 'key')->toArray();
        return view('legal.terms', compact('siteSettings'));
    }
}
