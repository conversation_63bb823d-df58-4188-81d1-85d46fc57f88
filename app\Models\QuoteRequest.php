<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuoteRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'name',
        'email',
        'phone',
        'company',
        'country',
        'inquiry_type',
        'product_interest',
        'message',
        'quantity',
        'delivery_destination',
        'packaging_requirements',
        'special_certifications',
        'preferred_contact_method',
        'timeline',
        'status',
    ];

    protected $casts = [
        'product_interest' => 'array',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
