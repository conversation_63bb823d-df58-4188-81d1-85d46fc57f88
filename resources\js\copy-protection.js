/**
 * Advanced Copy Protection System
 * Provides comprehensive protection against content copying
 */

class CopyProtection {
    constructor(options = {}) {
        this.options = {
            disableRightClick: true,
            disableTextSelection: true,
            disableKeyboardShortcuts: true,
            disableDeveloperTools: true,
            disableImageDragging: true,
            showWarnings: true,
            clearClipboard: false,
            watermark: null,
            ...options
        };

        this.init();
    }

    init() {
        if (this.options.disableRightClick) {
            this.disableRightClick();
        }

        if (this.options.disableTextSelection) {
            this.disableTextSelection();
        }

        if (this.options.disableKeyboardShortcuts) {
            this.disableKeyboardShortcuts();
        }

        if (this.options.disableDeveloperTools) {
            this.disableDeveloperTools();
        }

        if (this.options.disableImageDragging) {
            this.disableImageDragging();
        }

        if (this.options.showWarnings) {
            this.showConsoleWarning();
        }

        if (this.options.clearClipboard) {
            this.clearClipboardPeriodically();
        }

        if (this.options.watermark) {
            this.addWatermark(this.options.watermark);
        }

        this.detectDevTools();
        this.preventPrintScreen();
    }

    disableRightClick() {
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showProtectionMessage('Right-click is disabled to protect content.');
            return false;
        });
    }

    disableTextSelection() {
        document.addEventListener('selectstart', (e) => {
            e.preventDefault();
            return false;
        });

        document.addEventListener('mousedown', (e) => {
            if (e.detail > 1) { // Prevent multiple clicks
                e.preventDefault();
                return false;
            }
        });
    }

    disableKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Common copy/paste shortcuts
            const forbiddenKeys = [
                { ctrl: true, key: 65 }, // Ctrl+A (Select All)
                { ctrl: true, key: 67 }, // Ctrl+C (Copy)
                { ctrl: true, key: 86 }, // Ctrl+V (Paste)
                { ctrl: true, key: 88 }, // Ctrl+X (Cut)
                { ctrl: true, key: 83 }, // Ctrl+S (Save)
                { ctrl: true, key: 80 }, // Ctrl+P (Print)
                { ctrl: true, key: 85 }, // Ctrl+U (View Source)
                { key: 123 }, // F12 (Developer Tools)
                { ctrl: true, shift: true, key: 73 }, // Ctrl+Shift+I (Developer Tools)
                { ctrl: true, shift: true, key: 74 }, // Ctrl+Shift+J (Console)
                { ctrl: true, shift: true, key: 67 }, // Ctrl+Shift+C (Inspect Element)
            ];

            for (const forbidden of forbiddenKeys) {
                if (this.matchesKeyCombo(e, forbidden)) {
                    e.preventDefault();
                    this.showProtectionMessage('This keyboard shortcut is disabled.');
                    return false;
                }
            }
        });
    }

    matchesKeyCombo(event, combo) {
        return (!combo.ctrl || event.ctrlKey) &&
               (!combo.shift || event.shiftKey) &&
               (!combo.alt || event.altKey) &&
               event.keyCode === combo.key;
    }

    disableDeveloperTools() {
        // Detect if developer tools are open
        let devtools = {
            open: false,
            orientation: null
        };

        const threshold = 160;

        setInterval(() => {
            if (window.outerHeight - window.innerHeight > threshold || 
                window.outerWidth - window.innerWidth > threshold) {
                if (!devtools.open) {
                    devtools.open = true;
                    this.handleDevToolsOpen();
                }
            } else {
                devtools.open = false;
            }
        }, 500);
    }

    handleDevToolsOpen() {
        document.body.style.display = 'none';
        alert('Developer tools detected. Please close them to continue.');
        setTimeout(() => {
            document.body.style.display = 'block';
        }, 1000);
    }

    disableImageDragging() {
        document.addEventListener('dragstart', (e) => {
            if (e.target.tagName === 'IMG') {
                e.preventDefault();
                return false;
            }
        });

        // Apply to existing images
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            img.draggable = false;
            img.style.pointerEvents = 'none';
        });

        // Apply to future images
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                        images.forEach(img => {
                            img.draggable = false;
                            img.style.pointerEvents = 'none';
                        });
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    }

    showConsoleWarning() {
        console.clear();
        console.log('%cSTOP!', 'color: red; font-size: 50px; font-weight: bold;');
        console.log('%cThis is a browser feature intended for developers.', 'color: red; font-size: 16px;');
        console.log('%cContent on this website is protected by copyright law.', 'color: red; font-size: 16px;');
        console.log('%cUnauthorized copying or reproduction is prohibited.', 'color: red; font-size: 16px;');
    }

    clearClipboardPeriodically() {
        setInterval(() => {
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText('').catch(() => {
                    // Ignore errors
                });
            }
        }, 2000);
    }

    addWatermark(text) {
        const watermark = document.createElement('div');
        watermark.textContent = text;
        watermark.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 48px;
            color: rgba(0, 0, 0, 0.1);
            pointer-events: none;
            z-index: 1000;
            white-space: nowrap;
            font-weight: bold;
            text-transform: uppercase;
            user-select: none;
        `;
        document.body.appendChild(watermark);
    }

    detectDevTools() {
        let devtools = false;
        
        // Method 1: Console detection
        let consoleCheck = () => {
            let before = new Date();
            debugger;
            let after = new Date();
            if (after - before > 100) {
                this.handleDevToolsOpen();
            }
        };

        // Method 2: Window size detection
        let sizeCheck = () => {
            if (window.outerWidth - window.innerWidth > 200 || 
                window.outerHeight - window.innerHeight > 200) {
                this.handleDevToolsOpen();
            }
        };

        setInterval(consoleCheck, 1000);
        setInterval(sizeCheck, 1000);
    }

    preventPrintScreen() {
        document.addEventListener('keyup', (e) => {
            if (e.keyCode === 44) { // Print Screen
                document.body.style.display = 'none';
                setTimeout(() => {
                    document.body.style.display = 'block';
                }, 100);
                this.showProtectionMessage('Screenshot functionality is disabled.');
            }
        });
    }

    showProtectionMessage(message) {
        if (this.options.showWarnings) {
            // Create a temporary notification
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #f44336;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                z-index: 10000;
                font-size: 14px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }
    }
}

// Initialize copy protection when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check if copy protection is enabled via a global variable
    if (window.copyProtectionEnabled) {
        new CopyProtection({
            watermark: window.copyProtectionWatermark || null
        });
    }
});

// Export for use in other scripts
window.CopyProtection = CopyProtection;
