@extends('layouts.front')

@section('title', 'Contact Us - ' . ($siteSettings['company_name'] ?? config('app.name')))

@section('content')
    <!-- Page Title Section -->
    <div class="relative bg-gradient-to-r from-primary-red to-secondary-red text-white py-20 overflow-hidden"
         @if(isset($siteSettings['contact_hero_image']) && $siteSettings['contact_hero_image'] && file_exists(storage_path('app/public/' . $siteSettings['contact_hero_image'])))
         style="background-image: url('{{ asset('storage/' . $siteSettings['contact_hero_image']) }}'); background-size: cover; background-position: center;"
         @endif>
        @if(isset($siteSettings['contact_hero_image']) && $siteSettings['contact_hero_image'] && file_exists(storage_path('app/public/' . $siteSettings['contact_hero_image'])))
            <div class="absolute inset-0 bg-gradient-to-r from-primary-red/40 to-secondary-red/30"></div>
        @endif
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <div class="flex items-center justify-center mb-6">
                    <div class="bg-primary-gold text-primary-red rounded-full p-4 mr-4">
                        <i class="fas fa-phone text-3xl"></i>
                    </div>
                    <div class="text-left">
                        <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">Get In Touch</h3>
                        <h1 class="text-4xl md:text-6xl font-bold font-secondary">Contact Us</h1>
                    </div>
                </div>
                <p class="text-xl md:text-2xl text-red-100 mb-8 max-w-3xl mx-auto">Ready to discuss your poultry product needs? Our export team is here to help with quotes, specifications, and international shipping.</p>
                <div class="flex items-center justify-center space-x-2 text-red-100">
                    <a href="{{ route('home') }}" class="hover:text-white transition-colors">Home</a>
                    <i class="fas fa-chevron-right text-sm"></i>
                    <span>Contact Us</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Section -->
    <div class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Contact Information Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
                <!-- Phone -->
                <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-20 h-20 bg-gradient-to-br from-primary-red to-secondary-red rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-phone text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold font-secondary text-text-dark mb-4">Call Us</h3>
                    @if(isset($siteSettings['company_phone']) && $siteSettings['company_phone'])
                        <p class="text-gray-600 mb-4 text-lg">{{ $siteSettings['company_phone'] }}</p>
                        <a href="tel:{{ $siteSettings['company_phone'] }}" class="btn-primary text-sm px-4 py-2">
                            <i class="fas fa-phone mr-2"></i>
                            Call Now
                        </a>
                    @else
                        <p class="text-gray-600">Phone number not available</p>
                    @endif
                </div>

                <!-- Email -->
                <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-20 h-20 bg-gradient-to-br from-accent-blue to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-envelope text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold font-secondary text-text-dark mb-4">Email Us</h3>
                    @if(isset($siteSettings['company_email']) && $siteSettings['company_email'])
                        <p class="text-gray-600 mb-4 text-lg">{{ $siteSettings['company_email'] }}</p>
                        <a href="mailto:{{ $siteSettings['company_email'] }}" class="bg-accent-blue hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-envelope mr-2"></i>
                            Send Email
                        </a>
                    @else
                        <p class="text-gray-600">Email not available</p>
                    @endif
                </div>



                <!-- Processing Facility -->
                <div class="bg-white rounded-xl shadow-lg p-8 text-center hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                    <div class="w-20 h-20 bg-gradient-to-br from-success-green to-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-industry text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold font-secondary text-text-dark mb-4">Visit Our Facility</h3>
                    @if(isset($siteSettings['company_address']) && $siteSettings['company_address'])
                        <p class="text-gray-600 mb-4 text-lg">{!! nl2br(e($siteSettings['company_address'])) !!}</p>
                        <a href="#facility-map" class="bg-success-green hover:bg-green-600 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            View Location
                        </a>
                    @else
                        <p class="text-gray-600">Address not available</p>
                    @endif
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
                <!-- Contact Form -->
                <div class="bg-white rounded-xl shadow-xl p-10">
                    <div class="flex items-center mb-8">
                        <div class="bg-primary-red text-white rounded-full p-3 mr-4">
                            <i class="fas fa-paper-plane text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">Get In Touch</h3>
                            <h2 class="text-3xl font-bold font-secondary text-text-dark">Send us a Message</h2>
                        </div>
                    </div>

                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle mr-2"></i>
                                {{ session('success') }}
                            </div>
                        </div>
                    @endif

                    <!-- AJAX Message Container -->
                    <div id="contact-message" class="hidden"></div>

                    <form id="contact-form" action="{{ route('contact.store') }}" method="POST" class="space-y-6">
                        @csrf

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-bold text-text-dark mb-2">
                                    <i class="fas fa-user mr-2 text-primary-red"></i>Full Name *
                                </label>
                                <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                       class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red transition-colors"
                                       placeholder="Your full name">
                                @error('name')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="block text-sm font-bold text-text-dark mb-2">
                                    <i class="fas fa-envelope mr-2 text-primary-red"></i>Email Address *
                                </label>
                                <input type="email" name="email" id="email" value="{{ old('email') }}" required
                                       class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red transition-colors"
                                       placeholder="<EMAIL>">
                                @error('email')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Phone -->
                            <div>
                                <label for="phone" class="block text-sm font-bold text-text-dark mb-2">
                                    <i class="fas fa-phone mr-2 text-primary-red"></i>Phone Number
                                </label>
                                <input type="tel" name="phone" id="phone" value="{{ old('phone') }}"
                                       class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red transition-colors"
                                       placeholder="+268 2404 1234 (Eswatini)">
                                @error('phone')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Company -->
                            <div>
                                <label for="company" class="block text-sm font-bold text-text-dark mb-2">
                                    <i class="fas fa-building mr-2 text-primary-red"></i>Company/Organization
                                </label>
                                <input type="text" name="company" id="company" value="{{ old('company') }}"
                                       class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red transition-colors"
                                       placeholder="Your company or organization name">
                                @error('company')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Inquiry Type -->
                            <div>
                                <label for="inquiry_type" class="block text-sm font-bold text-text-dark mb-2">
                                    <i class="fas fa-question-circle mr-2 text-primary-red"></i>Inquiry Type *
                                </label>
                                <select name="inquiry_type" id="inquiry_type" required
                                        class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red transition-colors">
                                    <option value="">Select inquiry type</option>
                                    <option value="export" {{ old('inquiry_type') == 'export' ? 'selected' : '' }}>Export Inquiry</option>
                                    <option value="bulk_order" {{ old('inquiry_type') == 'bulk_order' ? 'selected' : '' }}>Bulk Order</option>
                                    <option value="pricing" {{ old('inquiry_type') == 'pricing' ? 'selected' : '' }}>Pricing & Quotes</option>
                                    <option value="quality" {{ old('inquiry_type') == 'quality' ? 'selected' : '' }}>Quality & Certifications</option>
                                    <option value="partnership" {{ old('inquiry_type') == 'partnership' ? 'selected' : '' }}>Distribution Partnership</option>
                                    <option value="facility_visit" {{ old('inquiry_type') == 'facility_visit' ? 'selected' : '' }}>Facility Visit</option>
                                    <option value="general" {{ old('inquiry_type') == 'general' ? 'selected' : '' }}>General Inquiry</option>
                                    <option value="other" {{ old('inquiry_type') == 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                @error('inquiry_type')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Branch (Dynamic - only show if branches exist) -->
                            @if($branches && $branches->count() > 1)
                                <div>
                                    <label for="branch_id" class="block text-sm font-bold text-text-dark mb-2">
                                        <i class="fas fa-map-marker-alt mr-2 text-primary-red"></i>Preferred Location
                                    </label>
                                    <select name="branch_id" id="branch_id"
                                            class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red transition-colors">
                                        <option value="">Any location</option>
                                        @foreach($branches as $branch)
                                            <option value="{{ $branch->id }}" {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }} - {{ $branch->city }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('branch_id')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            @else
                                <!-- Country/Region for international inquiries -->
                                <div>
                                    <label for="country" class="block text-sm font-bold text-text-dark mb-2">
                                        <i class="fas fa-globe mr-2 text-primary-red"></i>Country/Region
                                    </label>
                                    <input type="text" name="country" id="country" value="{{ old('country') }}"
                                           class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red transition-colors"
                                           placeholder="Your country or region">
                                    @error('country')
                                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            @endif
                        </div>

                        <!-- Subject -->
                        <div>
                            <label for="subject" class="block text-sm font-bold text-text-dark mb-2">
                                <i class="fas fa-tag mr-2 text-primary-red"></i>Subject *
                            </label>
                            <input type="text" name="subject" id="subject" value="{{ old('subject') }}" required
                                   class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red transition-colors"
                                   placeholder="Brief subject of your inquiry">
                            @error('subject')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Message -->
                        <div>
                            <label for="message" class="block text-sm font-bold text-text-dark mb-2">
                                <i class="fas fa-comment mr-2 text-primary-red"></i>Message *
                            </label>
                            <textarea name="message" id="message" rows="6" required
                                      class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red transition-colors resize-vertical"
                                      placeholder="Please provide details about your inquiry...">{{ old('message') }}</textarea>
                            @error('message')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Button -->
                        <div>
                            <button type="submit" id="contact-submit-btn" class="w-full bg-gradient-to-r from-primary-red to-secondary-red hover:from-secondary-red hover:to-primary-red text-white px-6 py-4 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg flex items-center justify-center">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Send Message
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Company Information -->
                <div class="space-y-8">
                    <!-- About Section -->
                    <div class="bg-white rounded-lg shadow-md p-8">
                        <h2 class="text-2xl font-bold mb-6">
                            <i class="fas fa-info-circle mr-2 text-primary-red"></i>
                            About {{ $siteSettings['company_name'] ?? 'Our Company' }}
                        </h2>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            {{ $siteSettings['about_us_content'] ?? 'We are a leading provider of industrial and commercial power solutions, specializing in compressors, generators, and inverters.' }}
                        </p>

                        @if(isset($siteSettings['delivery_nationwide']) && $siteSettings['delivery_nationwide'])
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                                <div class="flex items-center">
                                    <i class="fas fa-truck text-green-600 mr-3"></i>
                                    <div>
                                        <h4 class="font-semibold text-green-800">Nationwide Delivery</h4>
                                        <p class="text-green-700 text-sm">We deliver to all provinces across South Africa</p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                <span>Expert Consultation</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                <span>Quality Products</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                <span>Professional Installation</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                <span>After-sales Support</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Contact -->
                    <div class="bg-gradient-to-br from-primary-red to-secondary-red rounded-lg shadow-md p-8 text-white">
                        <h3 class="text-xl font-bold mb-4">
                            <i class="fas fa-headset mr-2"></i>
                            Need Immediate Assistance?
                        </h3>
                        <p class="mb-6 text-red-100">Our team is ready to help you with any questions or urgent requirements.</p>

                        <div class="space-y-3">
                            @if(isset($siteSettings['company_phone']) && $siteSettings['company_phone'])
                                <a href="tel:{{ $siteSettings['company_phone'] }}" class="flex items-center bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-3 transition-colors">
                                    <i class="fas fa-phone mr-3"></i>
                                    <span>{{ $siteSettings['company_phone'] }}</span>
                                </a>
                            @endif

                            @if(isset($siteSettings['company_email']) && $siteSettings['company_email'])
                                <a href="mailto:{{ $siteSettings['company_email'] }}" class="flex items-center bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-3 transition-colors">
                                    <i class="fas fa-envelope mr-3"></i>
                                    <span>{{ $siteSettings['company_email'] }}</span>
                                </a>
                            @endif

                            <button onclick="openQuoteModal()" class="w-full flex items-center justify-center bg-white text-primary-red hover:bg-gray-100 rounded-lg p-3 font-semibold transition-colors">
                                <i class="fas fa-quote-left mr-2"></i>
                                Request Quick Quote
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Processing Facilities Section -->
    @if($branches && $branches->count() > 0)
        <div id="facility-map" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <div class="flex items-center justify-center mb-6">
                        <div class="bg-primary-red text-white rounded-full p-3 mr-4">
                            <i class="fas fa-industry text-2xl"></i>
                        </div>
                        <div class="text-left">
                            <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">Our Facilities</h3>
                            <h2 class="text-4xl md:text-5xl font-bold font-secondary text-text-dark">
                                @if($branches->count() > 1)
                                    Processing Facilities
                                @else
                                    Processing Facility
                                @endif
                            </h2>
                        </div>
                    </div>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        @if($branches->count() > 1)
                            Visit our state-of-the-art poultry processing facilities across Eswatini, equipped with modern technology and international certifications.
                        @else
                            Visit our state-of-the-art poultry processing facility in Eswatini, equipped with modern technology and international certifications.
                        @endif
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-{{ $branches->count() > 2 ? '3' : ($branches->count() == 2 ? '2' : '1') }} gap-8">
                    @foreach($branches as $branch)
                        <div class="bg-white rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 p-8 border border-gray-100">
                            <div class="flex items-start mb-6">
                                <div class="w-16 h-16 bg-gradient-to-br from-primary-red to-secondary-red rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                                    <i class="fas fa-industry text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold font-secondary text-text-dark">{{ $branch->name }}</h3>
                                    <p class="text-primary-red font-semibold">{{ $branch->city }}@if($branch->province), {{ $branch->province }}@endif</p>
                                </div>
                            </div>

                            <!-- Facility Features -->
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="bg-success-green/10 rounded-lg p-3 text-center">
                                    <i class="fas fa-certificate text-success-green text-xl mb-2"></i>
                                    <div class="text-xs font-semibold text-success-green">Halal Certified</div>
                                </div>
                                <div class="bg-accent-blue/10 rounded-lg p-3 text-center">
                                    <i class="fas fa-shield-alt text-accent-blue text-xl mb-2"></i>
                                    <div class="text-xs font-semibold text-accent-blue">HACCP Compliant</div>
                                </div>
                            </div>

                            <div class="space-y-4 text-gray-600">
                                <!-- Address -->
                                <div class="flex items-start">
                                    <i class="fas fa-map-marker-alt mr-3 mt-1 text-primary-red flex-shrink-0"></i>
                                    <span class="text-sm">{{ $branch->full_address }}</span>
                                </div>

                                <!-- Phone -->
                                @if($branch->phone)
                                    <div class="flex items-center">
                                        <i class="fas fa-phone mr-3 text-primary-red flex-shrink-0"></i>
                                        <a href="tel:{{ $branch->phone }}" class="text-sm hover:text-primary-red transition-colors font-medium">
                                            {{ $branch->phone }}
                                        </a>
                                    </div>
                                @endif

                                <!-- Email -->
                                @if($branch->email)
                                    <div class="flex items-center">
                                        <i class="fas fa-envelope mr-3 text-primary-red flex-shrink-0"></i>
                                        <a href="mailto:{{ $branch->email }}" class="text-sm hover:text-primary-red transition-colors font-medium">
                                            {{ $branch->email }}
                                        </a>
                                    </div>
                                @endif

                                <!-- Manager -->
                                @if($branch->manager_name)
                                    <div class="flex items-center">
                                        <i class="fas fa-user-tie mr-3 text-primary-red flex-shrink-0"></i>
                                        <span class="text-sm">Manager: {{ $branch->manager_name }}</span>
                                    </div>
                                @endif

                                <!-- Operating Hours -->
                                @if($branch->operating_hours)
                                    <div class="flex items-start">
                                        <i class="fas fa-clock mr-3 mt-1 text-primary-red flex-shrink-0"></i>
                                        <div class="text-sm">
                                            <div class="font-medium text-gray-700 mb-1">Operating Hours:</div>
                                            <div class="text-gray-600">{!! nl2br(e($branch->operating_hours)) !!}</div>
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <!-- Action Buttons -->
                            <div class="mt-6 flex flex-col sm:flex-row gap-3">
                                @if($branch->phone)
                                    <a href="tel:{{ $branch->phone }}" class="flex-1 bg-primary-red hover:bg-secondary-red text-white px-4 py-2 rounded-lg text-center font-medium transition-colors text-sm">
                                        <i class="fas fa-phone mr-2"></i>Call
                                    </a>
                                @endif
                                @if($branch->email)
                                    <a href="mailto:{{ $branch->email }}" class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-lg text-center font-medium transition-colors text-sm">
                                        <i class="fas fa-envelope mr-2"></i>Email
                                    </a>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Contact CTA -->
                <div class="mt-12 text-center">
                    <div class="bg-gradient-to-r from-primary-red to-secondary-red rounded-lg p-8 text-white">
                        <h3 class="text-2xl font-bold mb-4">Can't find what you're looking for?</h3>
                        <p class="text-red-100 mb-6">Our team is here to help you with any questions or special requirements.</p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <button onclick="openQuoteModal()" class="bg-white text-primary-red hover:bg-gray-100 px-6 py-3 rounded-lg font-semibold transition-colors">
                                <i class="fas fa-quote-left mr-2"></i>
                                Request Quote
                            </button>
                            @if(isset($siteSettings['company_phone']) && $siteSettings['company_phone'])
                                <a href="tel:{{ $siteSettings['company_phone'] }}" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                    <i class="fas fa-phone mr-2"></i>
                                    Call {{ $siteSettings['company_phone'] }}
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize contact form AJAX
        initContactForm();
    });

    // Contact Form AJAX Handler
    function initContactForm() {
        const contactForm = document.getElementById('contact-form');
        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();
                handleContactSubmission(this);
            });
        }
    }

    // Contact Form Submission Handler
    function handleContactSubmission(form) {
        const submitBtn = form.querySelector('#contact-submit-btn');
        const messageDiv = document.getElementById('contact-message');

        // Clear previous messages and errors
        messageDiv.className = 'hidden';
        clearFormErrors(form);

        // Validate required fields
        const requiredFields = form.querySelectorAll('[required]');
        let hasErrors = false;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                showFieldError(field, 'This field is required.');
                hasErrors = true;
            } else if (field.type === 'email' && !isValidEmail(field.value)) {
                showFieldError(field, 'Please enter a valid email address.');
                hasErrors = true;
            }
        });

        if (hasErrors) {
            showMessage(messageDiv, 'Please correct the errors below.', 'error');
            return;
        }

        // Show loading state
        const originalBtnContent = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending Message...';
        submitBtn.disabled = true;

        // Prepare form data
        const formData = new FormData(form);

        // Send AJAX request
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                               form.querySelector('input[name="_token"]').value
            }
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => Promise.reject(data));
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                showMessage(messageDiv, data.message, 'success');
                form.reset(); // Clear the form
                clearFormErrors(form);

                // Show success alert
                alert('✅ Success!\n\n' + data.message);

                // Scroll to success message
                messageDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
            } else {
                showMessage(messageDiv, data.message || 'An error occurred. Please try again.', 'error');

                // Show error alert
                alert('❌ Error!\n\n' + (data.message || 'An error occurred. Please try again.'));
            }
        })
        .catch(error => {
            console.error('Contact form error:', error);

            // Handle validation errors
            if (error.errors) {
                Object.keys(error.errors).forEach(field => {
                    const fieldElement = form.querySelector(`[name="${field}"]`);
                    if (fieldElement) {
                        showFieldError(fieldElement, error.errors[field][0]);
                    }
                });
                showMessage(messageDiv, 'Please correct the errors below.', 'error');

                // Show validation error alert
                alert('❌ Validation Error!\n\nPlease correct the errors in the form and try again.');
            } else {
                showMessage(messageDiv, error.message || 'An error occurred. Please try again.', 'error');

                // Show general error alert
                alert('❌ Error!\n\n' + (error.message || 'An error occurred. Please try again.'));
            }
        })
        .finally(() => {
            // Restore button state
            submitBtn.innerHTML = originalBtnContent;
            submitBtn.disabled = false;
        });
    }

    // Utility Functions
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showMessage(messageDiv, message, type) {
        const isError = type === 'error';
        const bgColor = isError ? 'bg-red-100' : 'bg-green-100';
        const textColor = isError ? 'text-red-800' : 'text-green-800';
        const borderColor = isError ? 'border-red-200' : 'border-green-200';
        const icon = isError ? 'fas fa-exclamation-circle' : 'fas fa-check-circle';

        messageDiv.className = `${bgColor} ${textColor} border ${borderColor} rounded-lg p-4 mb-6`;
        messageDiv.innerHTML = `
            <div class="flex items-center">
                <i class="${icon} mr-3 text-lg"></i>
                <span class="font-medium">${message}</span>
            </div>
        `;

        // Auto-hide success messages after 8 seconds
        if (!isError) {
            setTimeout(() => {
                messageDiv.className = 'hidden';
            }, 8000);
        }
    }

    function showFieldError(field, message) {
        // Add error styling to field
        field.classList.add('border-red-500');
        field.classList.remove('border-gray-300');

        // Remove existing error message
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        // Add error message
        const errorDiv = document.createElement('p');
        errorDiv.className = 'text-red-500 text-sm mt-1 field-error';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    function clearFormErrors(form) {
        // Remove error styling from all fields
        const fields = form.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            field.classList.remove('border-red-500');
            field.classList.add('border-gray-300');
        });

        // Remove all error messages
        const errorMessages = form.querySelectorAll('.field-error');
        errorMessages.forEach(error => error.remove());
    }
</script>
@endpush