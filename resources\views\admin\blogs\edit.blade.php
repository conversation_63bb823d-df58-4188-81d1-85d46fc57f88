@extends('admin.layouts.app')

@section('title', 'Edit Blog Post')

@section('header', 'Edit Blog Post')

@push('styles')
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
@endpush

@section('content')
    <div class="bg-white rounded-lg shadow-md p-6">
        <form action="{{ route('admin.blogs.update', $blog) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Post Title</label>
                    <input type="text" name="title" id="title" value="{{ old('title', $blog->title) }}" class="w-full border-2 border-gray-300 rounded-md shadow-sm py-3 px-4 bg-white focus:border-primary-color focus:ring focus:ring-primary-color focus:ring-opacity-50 @error('title') border-red-500 @enderror" placeholder="Enter post title" required>
                    @error('title')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">Slug</label>
                    <input type="text" name="slug" id="slug" value="{{ old('slug', $blog->slug) }}" class="w-full border-2 border-gray-300 rounded-md shadow-sm py-3 px-4 bg-white focus:border-primary-color focus:ring focus:ring-primary-color focus:ring-opacity-50 @error('slug') border-red-500 @enderror" placeholder="post-slug" required>
                    @error('slug')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label for="blog_category_id" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <select name="blog_category_id" id="blog_category_id" class="w-full border-2 border-gray-300 rounded-md shadow-sm py-3 px-4 bg-white focus:border-primary-color focus:ring focus:ring-primary-color focus:ring-opacity-50 @error('blog_category_id') border-red-500 @enderror" required>
                        <option value="">Select Category</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}" {{ old('blog_category_id', $blog->blog_category_id) == $category->id ? 'selected' : '' }}>
                                {{ $category->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('blog_category_id')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" id="status" class="w-full border-2 border-gray-300 rounded-md shadow-sm py-3 px-4 bg-white focus:border-primary-color focus:ring focus:ring-primary-color focus:ring-opacity-50 @error('status') border-red-500 @enderror" required>
                        <option value="draft" {{ old('status', $blog->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                        <option value="published" {{ old('status', $blog->status) == 'published' ? 'selected' : '' }}>Published</option>
                    </select>
                    @error('status')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label for="published_at" class="block text-sm font-medium text-gray-700 mb-2">Publish Date</label>
                    <input type="text" name="published_at" id="published_at" value="{{ old('published_at', $blog->published_at ? $blog->published_at->format('Y-m-d H:i') : '') }}" class="w-full border-2 border-gray-300 rounded-md shadow-sm py-3 px-4 bg-white focus:border-primary-color focus:ring focus:ring-primary-color focus:ring-opacity-50 @error('published_at') border-red-500 @enderror" placeholder="Select date and time">
                    <p class="text-gray-500 text-sm mt-2">Leave empty to use current date and time.</p>
                    @error('published_at')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div>
                    <label for="featured" class="block text-sm font-medium text-gray-700 mb-2">Featured</label>
                    <div class="mt-2">
                        <label class="inline-flex items-center">
                            <input type="checkbox" name="featured" id="featured" value="1" {{ old('featured', $blog->featured) ? 'checked' : '' }} class="rounded border-gray-300 text-primary-color shadow-sm focus:border-primary-color focus:ring focus:ring-primary-color focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Mark as featured post</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="mb-6">
                <label for="excerpt" class="block text-sm font-medium text-gray-700 mb-2">Excerpt</label>
                <textarea name="excerpt" id="excerpt" rows="3" class="w-full border-2 border-gray-300 rounded-md shadow-sm py-3 px-4 bg-white focus:border-primary-color focus:ring focus:ring-primary-color focus:ring-opacity-50 @error('excerpt') border-red-500 @enderror" placeholder="Enter a brief excerpt of the post" required>{{ old('excerpt', $blog->excerpt) }}</textarea>
                @error('excerpt')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="mb-6">
                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Content</label>
                <textarea name="content" id="content" class="summernote @error('content') border-red-500 @enderror">{{ old('content', $blog->content) }}</textarea>
                @error('content')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="mb-6">
                <label for="featured_image" class="block text-sm font-medium text-gray-700 mb-2">Featured Image</label>
                
                @if($blog->featured_image)
                    <div class="mb-3 relative group">
                        <img src="{{ blog_image($blog->featured_image) }}" alt="{{ $blog->title }}" class="w-48 h-auto rounded-md border-2 border-gray-300">
                        <p class="text-sm text-gray-500 mt-1">Current featured image</p>
                        
                        <!-- Remove Image Button -->
                        <form action="{{ route('admin.blogs.remove-image', $blog) }}" method="POST" class="absolute top-2 right-2" onsubmit="return confirm('Are you sure you want to remove this image?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600 transition-colors">
                                <i class="fas fa-times"></i>
                            </button>
                        </form>
                    </div>
                @endif
                
                <div class="flex items-center justify-center w-full">
                    <label for="featured_image" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                            <svg class="w-8 h-8 mb-3 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                            </svg>
                            <p class="mb-1 text-sm text-gray-500"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                            <p class="text-xs text-gray-500">PNG, JPG or GIF (MAX. 2MB)</p>
                        </div>
                        <input id="featured_image" name="featured_image" type="file" class="hidden" accept="image/*" />
                    </label>
                </div>
                <p class="text-gray-500 text-sm mt-2">Leave empty to keep the current image.</p>
                @error('featured_image')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
            

            
            <div class="border-t border-gray-200 pt-4 mt-6">
                <h3 class="text-lg font-medium text-gray-700 mb-4">SEO Information (Optional)</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                        <input type="text" name="meta_title" id="meta_title" value="{{ old('meta_title', $blog->meta_title) }}" class="w-full border-2 border-gray-300 rounded-md shadow-sm py-3 px-4 bg-white focus:border-primary-color focus:ring focus:ring-primary-color focus:ring-opacity-50 @error('meta_title') border-red-500 @enderror" placeholder="Enter meta title">
                        @error('meta_title')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <div>
                        <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">Meta Keywords</label>
                        <input type="text" name="meta_keywords" id="meta_keywords" value="{{ old('meta_keywords', $blog->meta_keywords) }}" class="w-full border-2 border-gray-300 rounded-md shadow-sm py-3 px-4 bg-white focus:border-primary-color focus:ring focus:ring-primary-color focus:ring-opacity-50 @error('meta_keywords') border-red-500 @enderror" placeholder="keyword1, keyword2, keyword3">
                        @error('meta_keywords')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                
                <div class="mb-6">
                    <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                    <textarea name="meta_description" id="meta_description" rows="3" class="w-full border-2 border-gray-300 rounded-md shadow-sm py-3 px-4 bg-white focus:border-primary-color focus:ring focus:ring-primary-color focus:ring-opacity-50 @error('meta_description') border-red-500 @enderror" placeholder="Enter meta description">{{ old('meta_description', $blog->meta_description) }}</textarea>
                    @error('meta_description')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>
            </div>
            
            <div class="flex items-center justify-end">
                <a href="{{ route('admin.blogs.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-md font-medium shadow-md transition-all duration-200 hover:shadow-lg mr-2">
                    Cancel
                </a>
                <button type="submit" id="update-btn" class="bg-primary-color hover:bg-opacity-90 text-white px-4 py-2 rounded-md font-medium shadow-md transition-all duration-200 hover:shadow-lg transform hover:-translate-y-1">
                    <i class="fas fa-save mr-2"></i> Update Post
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize Summernote
            $('.summernote').summernote({
                height: 300,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'underline', 'clear']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                callbacks: {
                    onChange: function(contents, $editable) {
                        // Content changed
                    }
                }
            });

            // Initialize Flatpickr
            flatpickr("#published_at", {
                enableTime: true,
                dateFormat: "Y-m-d H:i",
            });

            // Handle file input display
            $('#featured_image').on('change', function() {
                const file = this.files[0];
                if (file) {
                    const fileName = file.name;
                    const fileSize = (file.size / 1024 / 1024).toFixed(2);

                    // Check file size (2MB limit)
                    if (fileSize > 2) {
                        alert('File size too large. Maximum size is 2MB.');
                        $(this).val('');
                        return;
                    }

                    // Check file type
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                    if (!allowedTypes.includes(file.type)) {
                        alert('Invalid file type. Please select a JPEG, PNG, or GIF image.');
                        $(this).val('');
                        return;
                    }

                    // Update the label text to show selected file
                    const label = $(this).closest('label').find('p').first();
                    label.html('<span class="font-semibold text-green-600">Selected: ' + fileName + '</span> (' + fileSize + 'MB)');
                }
            });

            // Prevent default form submission since we're using AJAX
            $('form[action*="blogs"]').on('submit', function(e) {
                e.preventDefault();
                return false;
            });

            // AJAX form submission
            $('#update-btn').on('click', function(e) {
                e.preventDefault(); // Prevent default form submission

                const form = $(this).closest('form');
                const submitBtn = $(this);

                // Disable button and show loading state
                submitBtn.prop('disabled', true);
                submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i> Updating...');

                // Get form data and ensure Summernote content is included
                const formData = new FormData(form[0]);

                // Manually add Summernote content (it might not be included in FormData)
                const summernoteContent = $('.summernote').summernote('code');
                formData.set('content', summernoteContent);

                // Ensure checkbox values are properly handled
                formData.set('featured', $('#featured').is(':checked') ? '1' : '0');

                // Manually ensure all required fields are included in FormData
                formData.set('title', $('[name="title"]').val());
                formData.set('slug', $('[name="slug"]').val());
                formData.set('blog_category_id', $('[name="blog_category_id"]').val());
                formData.set('excerpt', $('[name="excerpt"]').val());
                formData.set('status', $('[name="status"]').val());

                // Add optional fields
                formData.set('published_at', $('[name="published_at"]').val());
                formData.set('meta_title', $('[name="meta_title"]').val());
                formData.set('meta_description', $('[name="meta_description"]').val());
                formData.set('meta_keywords', $('[name="meta_keywords"]').val());

                // Validate required fields before sending
                const requiredFields = {
                    'title': formData.get('title'),
                    'slug': formData.get('slug'),
                    'blog_category_id': formData.get('blog_category_id'),
                    'excerpt': formData.get('excerpt'),
                    'content': summernoteContent,
                    'status': formData.get('status')
                };

                let missingFields = [];
                for (let [field, value] of Object.entries(requiredFields)) {
                    if (!value || value.trim() === '' || value === '<p><br></p>') {
                        missingFields.push(field);
                    }
                }

                if (missingFields.length > 0) {
                    alert('Please fill in the following required fields: ' + missingFields.join(', '));
                    submitBtn.prop('disabled', false);
                    submitBtn.html('<i class="fas fa-save mr-2"></i> Update Post');
                    return;
                }



                // Add the _method field for Laravel's method spoofing
                formData.append('_method', 'PUT');

                // Ensure CSRF token is included
                const csrfToken = $('meta[name="csrf-token"]').attr('content') || $('input[name="_token"]').val();
                if (csrfToken) {
                    formData.set('_token', csrfToken);
                }

                // Make AJAX request - construct the correct update URL
                const updateUrl = '{{ route("admin.blogs.update", $blog) }}';

                $.ajax({
                    url: updateUrl,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        // Show success message
                        alert('Blog post updated successfully!');

                        // Redirect to blog index
                        window.location.href = '{{ route("admin.blogs.index") }}';
                    },
                    error: function(xhr, status, error) {

                        // Re-enable button
                        submitBtn.prop('disabled', false);
                        submitBtn.html('<i class="fas fa-save mr-2"></i> Update Post');

                        // Show error message
                        if (xhr.status === 422) {
                            // Validation errors
                            const errors = xhr.responseJSON.errors;
                            let errorMessage = 'Validation errors:\n';
                            for (let field in errors) {
                                errorMessage += '- ' + errors[field].join('\n- ') + '\n';
                            }
                            alert(errorMessage);
                        } else {
                            alert('Error updating blog post: ' + (xhr.responseJSON?.message || error));
                        }
                    }
                });
            });



            // Auto-generate slug from title
            $('#title').on('input', function() {
                const title = $(this).val();
                const slug = title.toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .trim('-');
                $('#slug').val(slug);
            });
        });
    </script>
@endpush
