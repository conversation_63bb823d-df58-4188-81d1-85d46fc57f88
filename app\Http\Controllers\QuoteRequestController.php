<?php

namespace App\Http\Controllers;

use App\Models\QuoteRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Models\SiteSetting;

class QuoteRequestController extends Controller
{
    /**
     * Store a newly created quote request in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'country' => 'required|string|max:255',
            'inquiry_type' => 'nullable|string|max:255',
            'product_interest' => 'nullable|array',
            'product_interest.*' => 'string|max:255',
            'message' => 'required|string',
            'product_id' => 'nullable|exists:products,id',
            'quantity' => 'nullable|integer|min:1',
        ]);

        // Create the quote request with default status 'new'
        $quoteRequest = QuoteRequest::create([
            'product_id' => $request->product_id,
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'company' => $request->company,
            'country' => $request->country,
            'inquiry_type' => $request->inquiry_type,
            'product_interest' => $request->product_interest,
            'message' => $request->message,
            'quantity' => $request->quantity ?? 1,
            'preferred_contact_method' => 'email',
            'status' => 'new',
        ]);

        // Send notification emails
        try {
            $emails = SiteSetting::getEmailAddresses('quote_request_emails');
            foreach ($emails as $email) {
                Mail::to($email)->send(new \App\Mail\QuoteRequestNotification($quoteRequest->toArray()));
            }
        } catch (\Exception $e) {
            Log::error('Failed to send quote request notification: ' . $e->getMessage());
            // Don't let email failures affect the user experience
        }

        // Return JSON response for AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Your quote request has been submitted successfully! We will get back to you soon.'
            ]);
        }

        return redirect()->back()->with('success', 'Your quote request has been submitted successfully! We will get back to you soon.');
    }
}
