/* Advanced Copy Protection Styles */

/* Disable text selection globally */
.copy-protected,
.copy-protected * {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* Disable text selection highlighting */
.copy-protected::selection,
.copy-protected *::selection {
    background: transparent !important;
    color: inherit !important;
}

.copy-protected::-moz-selection,
.copy-protected *::-moz-selection {
    background: transparent !important;
    color: inherit !important;
}

/* Disable image dragging and selection */
.copy-protected img,
.copy-protected video,
.copy-protected canvas {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-user-drag: none !important;
    -webkit-touch-callout: none !important;
    pointer-events: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* Disable outline on focus for better UX while maintaining protection */
.copy-protected *:focus {
    outline: none !important;
}

/* Prevent text cursor on non-interactive elements */
.copy-protected p,
.copy-protected h1,
.copy-protected h2,
.copy-protected h3,
.copy-protected h4,
.copy-protected h5,
.copy-protected h6,
.copy-protected span,
.copy-protected div:not([contenteditable]) {
    cursor: default !important;
}

/* Allow normal interaction with form elements */
.copy-protected input,
.copy-protected textarea,
.copy-protected select,
.copy-protected button,
.copy-protected a {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

.copy-protected input[type="text"],
.copy-protected input[type="email"],
.copy-protected input[type="password"],
.copy-protected input[type="search"],
.copy-protected input[type="tel"],
.copy-protected input[type="url"],
.copy-protected textarea {
    cursor: text !important;
}

/* Disable print styles */
@media print {
    .copy-protected {
        display: none !important;
    }
    
    .copy-protected::before {
        content: "This content is protected and cannot be printed." !important;
        display: block !important;
        text-align: center !important;
        font-size: 24px !important;
        color: #000 !important;
        margin: 50px 0 !important;
    }
}

/* Disable screenshot overlay (limited effectiveness) */
.copy-protected::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    pointer-events: none;
    z-index: -1;
}

/* Blur content when developer tools might be open */
@media (max-height: 500px) and (min-width: 800px) {
    .copy-protected {
        filter: blur(5px);
        transition: filter 0.3s ease;
    }
}

/* Additional protection for code blocks */
.copy-protected pre,
.copy-protected code {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    pointer-events: none !important;
}

/* Disable text selection on tables */
.copy-protected table,
.copy-protected td,
.copy-protected th {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
}

/* Watermark overlay for additional protection */
.copy-protected.with-watermark::before {
    content: attr(data-watermark);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-size: 48px;
    color: rgba(0, 0, 0, 0.1);
    pointer-events: none;
    z-index: 1000;
    white-space: nowrap;
    font-weight: bold;
    text-transform: uppercase;
}
