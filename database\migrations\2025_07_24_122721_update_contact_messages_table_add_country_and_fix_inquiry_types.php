<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contact_messages', function (Blueprint $table) {
            // Add country field
            $table->string('country')->nullable()->after('company');

            // Update inquiry_type enum to include new values
            $table->dropColumn('inquiry_type');
        });

        Schema::table('contact_messages', function (Blueprint $table) {
            $table->enum('inquiry_type', ['export', 'bulk_order', 'pricing', 'quality', 'partnership', 'facility_visit', 'general', 'other'])->default('general')->after('branch_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contact_messages', function (Blueprint $table) {
            // Remove country field
            $table->dropColumn('country');

            // Revert inquiry_type enum to original values
            $table->dropColumn('inquiry_type');
        });

        Schema::table('contact_messages', function (Blueprint $table) {
            $table->enum('inquiry_type', ['general', 'quote', 'support', 'partnership', 'other'])->default('general')->after('branch_id');
        });
    }
};
