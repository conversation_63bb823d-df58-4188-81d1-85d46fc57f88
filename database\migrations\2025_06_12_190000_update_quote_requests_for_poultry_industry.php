<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quote_requests', function (Blueprint $table) {
            // Add new fields for poultry industry
            $table->string('country')->nullable()->after('company');
            $table->string('inquiry_type')->nullable()->after('country');
            
            // Update product_interest to be more flexible
            // The existing json column will be used for poultry product categories
            
            // Add export-specific fields
            $table->text('delivery_destination')->nullable()->after('message');
            $table->text('packaging_requirements')->nullable()->after('delivery_destination');
            $table->text('special_certifications')->nullable()->after('packaging_requirements');
            $table->string('preferred_contact_method')->default('email')->after('special_certifications');
            $table->string('timeline')->nullable()->after('preferred_contact_method');
            
            // Update status enum to include more relevant statuses
            $table->dropColumn('status');
        });
        
        // Re-add status column with new enum values
        Schema::table('quote_requests', function (Blueprint $table) {
            $table->enum('status', ['new', 'reviewing', 'quoted', 'negotiating', 'completed', 'cancelled'])
                  ->default('new')
                  ->after('timeline');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quote_requests', function (Blueprint $table) {
            // Remove new columns
            $table->dropColumn([
                'country',
                'inquiry_type', 
                'delivery_destination',
                'packaging_requirements',
                'special_certifications',
                'preferred_contact_method',
                'timeline',
                'status'
            ]);
        });
        
        // Restore original status column
        Schema::table('quote_requests', function (Blueprint $table) {
            $table->enum('status', ['new', 'processing', 'completed'])->default('new');
        });
    }
};
