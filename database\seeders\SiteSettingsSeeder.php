<?php

namespace Database\Seeders;

use App\Models\SiteSetting;
use App\Models\Branch;
use Illuminate\Database\Seeder;

class SiteSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Default site settings
        $settings = [
            [
                'key' => 'site_title',
                'value' => 'Swazi Poultry Processors Ltd - Premium Poultry Products',
                'type' => 'text',
                'description' => 'Main site title displayed in browser tab'
            ],
            [
                'key' => 'company_name',
                'value' => 'Swazi Poultry Processors Ltd',
                'type' => 'text',
                'description' => 'Company name used throughout the website'
            ],
            [
                'key' => 'company_tagline',
                'value' => 'Premium Poultry Products from Eswatini',
                'type' => 'text',
                'description' => 'Company tagline or slogan'
            ],
            [
                'key' => 'site_logo',
                'value' => null,
                'type' => 'image',
                'description' => 'Main site logo'
            ],
            [
                'key' => 'site_favicon',
                'value' => null,
                'type' => 'image',
                'description' => 'Site favicon (.ico file)'
            ],
            [
                'key' => 'currency_symbol',
                'value' => 'E',
                'type' => 'text',
                'description' => 'Currency symbol (Eswatini Lilangeni)'
            ],
            [
                'key' => 'currency_code',
                'value' => 'SZL',
                'type' => 'text',
                'description' => 'Currency code'
            ],
            [
                'key' => 'export_worldwide',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Whether company exports worldwide'
            ],
            [
                'key' => 'company_phone',
                'value' => '+268 2505 2000',
                'type' => 'text',
                'description' => 'Main company phone number'
            ],
            [
                'key' => 'company_email',
                'value' => '<EMAIL>',
                'type' => 'text',
                'description' => 'Main company email address'
            ],
            [
                'key' => 'company_address',
                'value' => 'F7MW+HR5, Matsapha, Eswatini',
                'type' => 'text',
                'description' => 'Main company address'
            ],
            [
                'key' => 'about_us_content',
                'value' => 'Established as a leading poultry processor in Eswatini, we specialize in producing high-quality, halal-certified poultry products for both domestic and international markets. Our state-of-the-art processing facility ensures the highest standards of food safety and quality.',
                'type' => 'textarea',
                'description' => 'About us content for homepage and about page'
            ],
            [
                'key' => 'halal_certified',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Whether products are halal certified'
            ],
            [
                'key' => 'export_countries',
                'value' => 'South Africa, Botswana, Mozambique, Zambia, Middle East',
                'type' => 'text',
                'description' => 'Countries we export to'
            ],
            [
                'key' => 'processing_capacity',
                'value' => '10,000 birds per day',
                'type' => 'text',
                'description' => 'Daily processing capacity'
            ]
        ];

        foreach ($settings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }

        // Default branches
        $branches = [
            [
                'name' => 'Main Processing Plant',
                'city' => 'Matsapha',
                'province' => 'Manzini Region',
                'address' => 'F7MW+HR5, Matsapha Industrial Site',
                'phone' => '+268 2505 2000',
                'email' => '<EMAIL>',
                'manager_name' => 'Sipho Dlamini',
                'operating_hours' => 'Mon-Fri: 6:00 AM - 6:00 PM, Sat: 6:00 AM - 2:00 PM',
                'latitude' => -26.4833,
                'longitude' => 31.3167,
                'active' => true,
                'sort_order' => 0
            ],
            [
                'name' => 'Export Office',
                'city' => 'Matsapha',
                'province' => 'Manzini Region',
                'address' => 'F7MW+HR5, Matsapha Industrial Site',
                'phone' => '+268 2505 2001',
                'email' => '<EMAIL>',
                'manager_name' => 'Nomsa Mthembu',
                'operating_hours' => 'Mon-Fri: 8:00 AM - 5:00 PM',
                'latitude' => -26.4833,
                'longitude' => 31.3167,
                'active' => true,
                'sort_order' => 1
            ]
        ];

        foreach ($branches as $branch) {
            Branch::updateOrCreate(
                ['name' => $branch['name']],
                $branch
            );
        }
    }
}
