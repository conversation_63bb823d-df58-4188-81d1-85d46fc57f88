<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>@yield('title', $siteSettings['site_title'] ?? config('app.name', 'Laravel'))</title>

        <!-- Additional Meta Tags -->
        @stack('meta')

        <!-- Favicons -->
        <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/favicons/apple-touch-icon.png') }}">
        <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/favicons/favicon-32x32.png') }}">
        <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/favicons/favicon-16x16.png') }}">
        <link rel="manifest" href="{{ asset('images/favicons/site.webmanifest') }}">
        <link rel="mask-icon" href="{{ asset('images/favicons/safari-pinned-tab.svg') }}" color="#C41E3A">
        <link rel="shortcut icon" href="{{ asset('images/favicons/favicon.ico') }}">
        <meta name="msapplication-TileColor" content="#C41E3A">
        <meta name="msapplication-config" content="{{ asset('images/favicons/browserconfig.xml') }}">
        <meta name="theme-color" content="#C41E3A">

        <!-- Fallback to site settings favicon if available -->
        @if(isset($siteSettings['site_favicon']) && $siteSettings['site_favicon'])
            <link rel="icon" type="image/x-icon" href="{{ asset('storage/' . $siteSettings['site_favicon']) }}">
        @endif

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

        <!-- Tailwind CSS -->
        <script src="https://cdn.tailwindcss.com"></script>
        <script>
            tailwind.config = {
                theme: {
                    extend: {
                        colors: {
                            'primary-red': '#C41E3A',
                            'primary-gold': '#FFD700',
                            'secondary-red': '#A01729',
                            'secondary-gold': '#E6C200',
                            'accent-orange': '#E67E22',
                            'accent-blue': '#3498DB',
                            'success-green': '#27AE60',
                            'text-dark': '#2C3E50',
                            'text-light': '#ECF0F1'
                        },
                        fontFamily: {
                            'primary': ['Inter', 'system-ui', 'sans-serif'],
                            'secondary': ['Poppins', 'system-ui', 'sans-serif']
                        }
                    }
                }
            }
        </script>

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        @if(isset($siteSettings['copy_protection_enabled']) && $siteSettings['copy_protection_enabled'])
        <!-- Copy Protection CSS -->
        <link rel="stylesheet" href="{{ asset('css/copy-protection.css') }}">
        @endif

        <!-- Alpine.js -->
        <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- Custom Styles -->
        <style>
            body {
                font-family: 'Inter', system-ui, sans-serif;
            }

            .font-primary {
                font-family: 'Inter', system-ui, sans-serif;
            }

            .font-secondary {
                font-family: 'Poppins', system-ui, sans-serif;
            }

            .btn-primary {
                background-color: #C41E3A;
                color: white;
                padding: 0.75rem 1.5rem;
                border-radius: 0.375rem;
                font-weight: 600;
                text-decoration: none;
                display: inline-block;
                transition: all 0.2s;
                border: none;
                cursor: pointer;
            }

            .btn-primary:hover {
                background-color: #A01729;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(196, 30, 58, 0.3);
            }

            /* Copy Protection Styles */
            @if(isset($siteSettings['copy_protection_enabled']) && $siteSettings['copy_protection_enabled'])
            .copy-protected {
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
                -webkit-touch-callout: none;
                -webkit-tap-highlight-color: transparent;
            }

            .copy-protected::selection {
                background: transparent;
            }

            .copy-protected::-moz-selection {
                background: transparent;
            }

            /* Disable text selection on images */
            .copy-protected img {
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                user-select: none;
                -webkit-user-drag: none;
                -webkit-touch-callout: none;
                pointer-events: none;
            }

            /* Disable highlighting */
            .copy-protected *::selection {
                background: transparent !important;
            }

            .copy-protected *::-moz-selection {
                background: transparent !important;
            }
            @endif

            .btn-secondary {
                background-color: #E67E22;
                color: white;
                padding: 0.75rem 1.5rem;
                border-radius: 0.375rem;
                font-weight: 600;
                text-decoration: none;
                display: inline-block;
                transition: all 0.2s;
                border: none;
                cursor: pointer;
            }

            .btn-secondary:hover {
                background-color: #D35400;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
            }

            .line-clamp-2 {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            /* Desktop Navigation Styles */
            .nav-link {
                display: flex;
                align-items: center;
                padding: 0.75rem 1rem;
                font-size: 1rem;
                font-weight: 500;
                color: #2C3E50;
                text-decoration: none;
                border-radius: 0.5rem;
                transition: all 0.2s ease-in-out;
                position: relative;
            }

            .nav-link:hover {
                color: #C41E3A;
                background-color: #f8f9fa;
                transform: translateY(-1px);
            }

            .nav-link-active {
                color: #C41E3A !important;
                background-color: #fef2f2;
                border: 1px solid #fecaca;
            }

            .nav-link-active::after {
                content: '';
                position: absolute;
                bottom: -2px;
                left: 50%;
                transform: translateX(-50%);
                width: 80%;
                height: 3px;
                background-color: #33c4aa;
                border-radius: 2px;
            }

            /* Mobile Navigation Styles */
            .mobile-nav-link {
                display: flex;
                align-items: center;
                padding: 1rem;
                font-size: 1.125rem;
                font-weight: 600;
                color: #374151;
                text-decoration: none;
                border-radius: 0.75rem;
                transition: all 0.2s ease-in-out;
                border: 1px solid transparent;
            }

            .mobile-nav-link:hover {
                color: #33c4aa;
                background-color: #f3f4f6;
                border-color: #d1d5db;
            }

            .mobile-nav-link-active {
                color: #33c4aa !important;
                background-color: #ecfdf5;
                border-color: #a7f3d0;
                box-shadow: 0 2px 4px rgba(51, 196, 170, 0.1);
            }

            /* Enhanced Mobile Menu Animation */
            #mobile-menu {
                transition: all 0.3s ease-in-out;
                transform: translateY(-10px);
                opacity: 0;
            }

            #mobile-menu:not(.hidden) {
                transform: translateY(0);
                opacity: 1;
            }

            /* Logo Enhancement */
            .logo-icon {
                animation: rotate 20s linear infinite;
            }

            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            /* Responsive Font Sizes */
            @media (max-width: 640px) {
                .nav-link {
                    font-size: 0.875rem;
                    padding: 0.5rem 0.75rem;
                }

                .mobile-nav-link {
                    font-size: 1rem;
                    padding: 0.875rem;
                }
            }

            /* Footer Responsive Adjustments */
            @media (min-width: 1280px) {
                .max-w-8xl {
                    max-width: 88rem;
                }
            }

            /* Footer Column Spacing */
            .footer-grid {
                display: grid;
                gap: 2rem;
                grid-template-columns: 1fr;
            }

            @media (min-width: 768px) {
                .footer-grid {
                    grid-template-columns: repeat(2, 1fr);
                    gap: 2rem;
                }
            }

            @media (min-width: 1024px) {
                .footer-grid {
                    grid-template-columns: repeat(3, 1fr);
                    gap: 1.5rem;
                }
            }

            @media (min-width: 1280px) {
                .footer-grid {
                    grid-template-columns: 2fr 1.5fr 1.5fr 1.5fr 1.5fr;
                    gap: 2rem;
                }
            }
        </style>

        @stack('styles')

        <!-- Structured Data -->
        @stack('structured-data')
    </head>
    <body class="font-sans antialiased{{ isset($siteSettings['copy_protection_enabled']) && $siteSettings['copy_protection_enabled'] ? ' copy-protected' : '' }}">
        <div class="min-h-screen bg-white">
            <!-- Top Certification Bar -->
            <div class="bg-primary-red text-white py-2">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex flex-col sm:flex-row justify-between items-center text-sm">
                        <div class="flex items-center space-x-6 mb-2 sm:mb-0">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-certificate text-primary-gold"></i>
                                <span class="font-medium">Halal Certified</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-globe text-primary-gold"></i>
                                <span class="font-medium">Export Quality</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-shield-alt text-primary-gold"></i>
                                <span class="font-medium">HACCP Compliant</span>
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6">
                            @if(isset($siteSettings['company_phone']) && $siteSettings['company_phone'])
                                <div class="flex items-center">
                                    <i class="fas fa-phone mr-2 text-primary-gold"></i>
                                    <span>Call: <a href="tel:{{ $siteSettings['company_phone'] }}" class="hover:text-primary-gold transition-colors">{{ $siteSettings['company_phone'] }}</a></span>
                                </div>
                            @endif
                            @if(isset($siteSettings['company_email']) && $siteSettings['company_email'])
                                <div class="flex items-center">
                                    <i class="fas fa-envelope mr-2 text-primary-gold"></i>
                                    <span>Email: <a href="mailto:{{ $siteSettings['company_email'] }}" class="hover:text-primary-gold transition-colors">{{ $siteSettings['company_email'] }}</a></span>
                                </div>
                            @endif
                            @if(isset($siteSettings['export_worldwide']) && $siteSettings['export_worldwide'])
                                <div class="flex items-center">
                                    <i class="fas fa-shipping-fast mr-2 text-primary-gold"></i>
                                    <span class="text-sm font-medium">Worldwide Export</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Header -->
            <header class="bg-white shadow-lg sticky top-0 z-50 border-b-3 border-primary-red">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center py-4 lg:py-6">
                        <!-- Logo -->
                        <div class="flex items-center">
                            <a href="{{ route('home') }}" class="flex items-center text-xl lg:text-2xl font-bold font-secondary text-primary-red hover:text-secondary-red transition-colors">
                                @if(isset($siteSettings['site_logo']) && $siteSettings['site_logo'])
                                    <!-- Logo Image Only -->
                                    <img src="{{ asset('storage/' . $siteSettings['site_logo']) }}" alt="{{ $siteSettings['company_name'] ?? config('app.name') }}" class="h-24 lg:h-28 w-auto object-contain max-w-sm">
                                @else
                                    <!-- Text Logo with Icon -->
                                    <div class="bg-primary-red text-white rounded-full p-2 mr-3">
                                        <i class="fas fa-drumstick-bite text-xl lg:text-2xl"></i>
                                    </div>
                                    <div class="flex flex-col">
                                        <span class="text-lg lg:text-xl leading-tight">{{ $siteSettings['company_name'] ?? 'Swazi Poultry Processors Ltd' }}</span>
                                        <span class="text-xs lg:text-sm text-primary-gold font-medium">Premium Poultry Products</span>
                                    </div>
                                @endif
                            </a>
                        </div>

                        <!-- Desktop Navigation -->
                        <nav class="hidden lg:flex items-center space-x-1">
                            <a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'nav-link-active' : '' }}">
                                <span>Home</span>
                            </a>
                            <a href="{{ route('about.index') }}" class="nav-link {{ request()->routeIs('about.*') ? 'nav-link-active' : '' }}">
                                <span>About</span>
                            </a>
                            <a href="{{ route('products.index') }}" class="nav-link {{ request()->routeIs('products.*') ? 'nav-link-active' : '' }}">
                                <span>Products</span>
                            </a>
                            <a href="{{ route('shipping-returns') }}" class="nav-link {{ request()->routeIs('shipping-returns') ? 'nav-link-active' : '' }}">
                                <span>Shipping & Returns</span>
                            </a>
                            <a href="{{ route('blog.index') }}" class="nav-link {{ request()->routeIs('blog.*') ? 'nav-link-active' : '' }}">
                                <span>Blog</span>
                            </a>
                            <a href="{{ route('testimonials.index') }}" class="nav-link {{ request()->routeIs('testimonials.*') ? 'nav-link-active' : '' }}">
                                <span>Reviews</span>
                            </a>
                            <a href="{{ route('contact.index') }}" class="nav-link {{ request()->routeIs('contact.*') ? 'nav-link-active' : '' }}">
                                <span>Contact</span>
                            </a>
                        </nav>

                        <!-- Right Side Actions -->
                        <div class="flex items-center space-x-3">
                            <!-- Request Quote Button -->
                            <button onclick="openQuoteModal()" class="hidden sm:flex items-center bg-accent-orange hover:bg-orange-600 text-white px-4 py-2 rounded-lg font-semibold text-sm transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg">
                                <i class="fas fa-envelope mr-2"></i>
                                <span class="hidden md:inline">Request Quote</span>
                                <span class="md:hidden">Quote</span>
                            </button>

                            <!-- Mobile Menu Toggle -->
                            <button id="mobile-menu-toggle" class="lg:hidden focus:outline-none p-2 rounded-lg hover:bg-gray-100 transition-colors">
                                <i class="fas fa-bars text-2xl text-primary-red"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="lg:hidden hidden bg-white shadow-xl border-t-3 border-primary-red">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                    <nav class="flex flex-col space-y-2">
                        <!-- Mobile Navigation Links -->
                        <a href="{{ route('home') }}" class="mobile-nav-link {{ request()->routeIs('home') ? 'mobile-nav-link-active' : '' }}">
                            <span>Home</span>
                            @if(request()->routeIs('home'))
                                <i class="fas fa-chevron-right ml-auto text-primary-red"></i>
                            @endif
                        </a>

                        <a href="{{ route('about.index') }}" class="mobile-nav-link {{ request()->routeIs('about.*') ? 'mobile-nav-link-active' : '' }}">
                            <span>About Us</span>
                            @if(request()->routeIs('about.*'))
                                <i class="fas fa-chevron-right ml-auto text-primary-red"></i>
                            @endif
                        </a>

                        <a href="{{ route('products.index') }}" class="mobile-nav-link {{ request()->routeIs('products.*') ? 'mobile-nav-link-active' : '' }}">
                            <span>Products</span>
                            @if(request()->routeIs('products.*'))
                                <i class="fas fa-chevron-right ml-auto text-primary-red"></i>
                            @endif
                        </a>

                        <a href="{{ route('shipping-returns') }}" class="mobile-nav-link {{ request()->routeIs('shipping-returns') ? 'mobile-nav-link-active' : '' }}">
                            <span>Shipping & Returns</span>
                            @if(request()->routeIs('shipping-returns'))
                                <i class="fas fa-chevron-right ml-auto text-primary-red"></i>
                            @endif
                        </a>

                        <a href="{{ route('blog.index') }}" class="mobile-nav-link {{ request()->routeIs('blog.*') ? 'mobile-nav-link-active' : '' }}">
                            <span>News & Updates</span>
                            @if(request()->routeIs('blog.*'))
                                <i class="fas fa-chevron-right ml-auto text-primary-red"></i>
                            @endif
                        </a>

                        <a href="{{ route('contact.index') }}" class="mobile-nav-link {{ request()->routeIs('contact.*') ? 'mobile-nav-link-active' : '' }}">
                            <span>Contact Us</span>
                            @if(request()->routeIs('contact.*'))
                                <i class="fas fa-chevron-right ml-auto text-primary-red"></i>
                            @endif
                        </a>

                        <!-- Mobile Action Buttons -->
                        <div class="pt-4 mt-4 border-t border-gray-200 space-y-3">
                            <button onclick="openQuoteModal()" class="w-full flex items-center justify-center bg-accent-orange hover:bg-orange-600 text-white px-4 py-3 rounded-lg font-semibold text-base transition-all duration-200 transform hover:scale-105 shadow-md">
                                <i class="fas fa-envelope mr-2"></i>
                                <span>Request Quote</span>
                            </button>
                        </div>
                    </nav>
                </div>
            </div>

            <!-- Page Content -->
            <main>
                @yield('content')
            </main>

            <!-- Footer -->
            <footer class="bg-gray-800 text-white py-12">
                <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
                    <div class="footer-grid">
                        <div>
                            <h3 class="text-xl font-semibold mb-4">{{ $siteSettings['company_name'] ?? config('app.name', 'Laravel') }}</h3>
                            <p class="text-gray-400 mb-6">
                                {{ $siteSettings['about_us_content'] ?? 'Quality compressors, generators, and power solutions for all your industrial and commercial needs.' }}
                            </p>

                            
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                            <ul class="space-y-2">
                                <li><a href="{{ route('home') }}" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                                <li><a href="{{ route('products.index') }}" class="text-gray-400 hover:text-white transition-colors">Products</a></li>
                                <li><a href="#" class="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                                <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                                <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Blog</a></li>
                                <li><a href="{{route('testimonials.index')}}" class="text-gray-400 hover:text-white transition-colors">Testimonials</a></li>
                            </ul>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold mb-4">Contact Us</h3>
                            <ul class="space-y-3 text-gray-400">
                                @if(isset($siteSettings['company_address']) && $siteSettings['company_address'])
                                    <li class="flex items-start">
                                        <i class="fas fa-map-marker-alt mr-3 mt-1 text-primary-color"></i>
                                        <span>{!! nl2br(e($siteSettings['company_address'])) !!}</span>
                                    </li>
                                @endif
                                @if(isset($siteSettings['company_phone']) && $siteSettings['company_phone'])
                                    <li class="flex items-center">
                                        <i class="fas fa-phone mr-3 text-primary-color"></i>
                                        <a href="tel:{{ $siteSettings['company_phone'] }}" class="hover:text-white transition-colors">{{ $siteSettings['company_phone'] }}</a>
                                    </li>
                                @endif
                                @if(isset($siteSettings['company_email']) && $siteSettings['company_email'])
                                    <li class="flex items-center">
                                        <i class="fas fa-envelope mr-3 text-primary-color"></i>
                                        <a href="mailto:{{ $siteSettings['company_email'] }}" class="hover:text-white transition-colors">{{ $siteSettings['company_email'] }}</a>
                                    </li>
                                @endif
                                @if(isset($siteSettings['delivery_nationwide']) && $siteSettings['delivery_nationwide'])
                                    <li class="flex items-center">
                                        <i class="fas fa-truck mr-3 text-primary-color"></i>
                                        <span class="text-sm">Nationwide Delivery Available</span>
                                    </li>
                                @endif
                            </ul>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold mb-4">Newsletter</h3>
                            <p class="text-gray-400 mb-4 text-sm">Subscribe to our newsletter for updates and special offers</p>
                            <form id="footer-newsletter-form" action="{{ route('newsletters.store') }}" method="POST" class="space-y-3">
                                @csrf
                                <div class="flex">
                                    <input type="email" name="email" id="footer-newsletter-email" placeholder="Your email" required
                                           class="px-3 py-2 w-full rounded-l-md focus:outline-none text-gray-800 text-sm border border-gray-300">
                                    <button type="submit" id="footer-newsletter-btn" class="bg-primary-color hover:bg-secondary-color px-4 py-2 rounded-r-md transition-colors">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                </div>
                                <div id="footer-newsletter-message" class="hidden"></div>
                                <p class="text-xs text-gray-500">We respect your privacy. Unsubscribe at any time.</p>
                            </form>
                        </div>

                        <div>
                            <h3 class="text-lg font-semibold mb-4">Our Branches</h3>
                            @if($branches->count() > 0)
                                <ul class="space-y-3 text-gray-400">
                                    @foreach($branches->take(2) as $branch)
                                        <li class="flex items-start">
                                            <i class="fas fa-building mr-2 mt-1 text-primary-color text-sm"></i>
                                            <div>
                                                <div class="font-medium text-gray-300 text-sm">{{ $branch->name }}</div>
                                                <div class="text-xs">{{ $branch->city }}@if($branch->province), {{ $branch->province }}@endif</div>
                                                @if($branch->phone)
                                                    <div class="text-xs mt-1">
                                                        <a href="tel:{{ $branch->phone }}" class="hover:text-white transition-colors">{{ $branch->phone }}</a>
                                                    </div>
                                                @endif
                                            </div>
                                        </li>
                                    @endforeach
                                    @if($branches->count() > 2)
                                        <li class="text-xs">
                                            <a href="#" class="text-primary-color hover:text-white transition-colors">
                                                View all {{ $branches->count() }} branches →
                                            </a>
                                        </li>
                                    @endif
                                </ul>
                            @else
                                <div class="text-gray-400 text-sm space-y-3">
                                    <div>
                                        <p class="font-medium text-gray-300">Cape Town</p>
                                        <p class="text-xs">45 Industrial Road</p>
                                        <p class="text-xs">+27 21 555 0123</p>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-300">Johannesburg</p>
                                        <p class="text-xs">123 Industrial Ave</p>
                                        <p class="text-xs">+27 11 123 4567</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="border-t border-gray-700 mt-8 pt-8">
                        <!-- Single Row - Copyright, Legal Links, and Social -->
                        <div class="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
                            <!-- Left - Copyright -->
                            <p class="text-gray-400">© {{ date('Y') }} {{ $siteSettings['company_name'] ?? config('app.name', 'Laravel') }}. All rights reserved.</p>

                            <!-- Right - Legal Links and Social Media -->
                            <div class="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-8">
                                <!-- Legal Links -->
                                <div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6">
                                    <a href="{{ route('legal.privacy') }}" class="text-gray-400 hover:text-white transition-colors text-sm">
                                        <i class="fas fa-shield-alt mr-2"></i>Privacy Policy
                                    </a>
                                    <a href="{{ route('legal.warranty') }}" class="text-gray-400 hover:text-white transition-colors text-sm">
                                        <i class="fas fa-certificate mr-2"></i>Warranty
                                    </a>
                                    <a href="{{ route('legal.terms') }}" class="text-gray-400 hover:text-white transition-colors text-sm">
                                        <i class="fas fa-file-contract mr-2"></i>Terms
                                    </a>
                                </div>

                                <!-- Social Media Icons -->
                                <div class="flex space-x-4">
                                    <a href="#" class="text-gray-400 hover:text-white transition-colors" title="Facebook">
                                        <i class="fab fa-facebook-f text-xl"></i>
                                    </a>
                                    <a href="#" class="text-gray-400 hover:text-white transition-colors" title="X (Twitter)">
                                        <i class="fab fa-x-twitter text-xl"></i>
                                    </a>
                                    <a href="#" class="text-gray-400 hover:text-white transition-colors" title="LinkedIn">
                                        <i class="fab fa-linkedin-in text-xl"></i>
                                    </a>
                                    <a href="#" class="text-gray-400 hover:text-white transition-colors" title="Instagram">
                                        <i class="fab fa-instagram text-xl"></i>
                                    </a>
                                    <a href="#" class="text-gray-400 hover:text-white transition-colors" title="WhatsApp">
                                        <i class="fab fa-whatsapp text-xl"></i>
                                    </a>
                                    <a href="#" class="text-gray-400 hover:text-white transition-colors" title="TikTok">
                                        <i class="fab fa-tiktok text-xl"></i>
                                    </a>
                                    <a href="#" class="text-gray-400 hover:text-white transition-colors" title="YouTube">
                                        <i class="fab fa-youtube text-xl"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        </div>

        <!-- Floating WhatsApp Button -->
        @if(isset($siteSettings['whatsapp_enabled']) && $siteSettings['whatsapp_enabled'] && isset($siteSettings['whatsapp_number']) && $siteSettings['whatsapp_number'])
            <div id="whatsapp-float" class="fixed bottom-6 left-6 z-40 group">
                <!-- Main WhatsApp Button -->
                <a href="https://wa.me/{{ str_replace(['+', ' ', '-', '(', ')'], '', $siteSettings['whatsapp_number']) }}?text=Hi%20there!%20I%20have%20a%20question%20about%20your%20services."
                   target="_blank" rel="noopener noreferrer"
                   class="flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 animate-pulse">
                    <i class="fab fa-whatsapp text-2xl"></i>
                </a>

                <!-- Tooltip -->
                <div class="absolute left-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                    Chat with us on WhatsApp
                    <div class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-0 h-0 border-t-4 border-b-4 border-r-4 border-transparent border-r-gray-800"></div>
                </div>

                <!-- Notification Badge (optional) -->
                <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                    <span class="text-white text-xs font-bold">1</span>
                </div>
            </div>
        @endif

        <!-- Export Inquiry Modal -->
        <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden" id="quote-modal">
            <div class="bg-white rounded-xl shadow-2xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
                <div class="p-8">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <div class="bg-primary-red text-white rounded-full p-3 mr-4">
                                <i class="fas fa-shipping-fast text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">Export Inquiry</h3>
                                <h2 class="text-2xl font-bold font-secondary text-text-dark">Request Quote</h2>
                            </div>
                        </div>
                        <button class="text-gray-500 hover:text-gray-700 focus:outline-none" id="close-modal">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Certification Badges -->
                    <div class="flex items-center justify-center space-x-4 mb-6">
                        <div class="bg-success-green/10 text-success-green px-3 py-1 rounded-full text-xs font-bold">
                            <i class="fas fa-certificate mr-1"></i>Halal Certified
                        </div>
                        <div class="bg-accent-blue/10 text-accent-blue px-3 py-1 rounded-full text-xs font-bold">
                            <i class="fas fa-shield-alt mr-1"></i>HACCP Compliant
                        </div>
                        <div class="bg-accent-orange/10 text-accent-orange px-3 py-1 rounded-full text-xs font-bold">
                            <i class="fas fa-globe mr-1"></i>Export Quality
                        </div>
                    </div>
                    <!-- AJAX Message Container -->
                    <div id="global-quote-message" class="hidden mb-4"></div>

                    <form id="global-quote-form" action="{{ route('quote-requests.store') }}" method="POST">
                        @csrf
                        <!-- Hidden product field for specific product quotes -->
                        <input type="hidden" name="product_id" id="global-product-id" value="">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="name" class="block text-text-dark font-semibold mb-2">
                                    <i class="fas fa-user mr-2 text-primary-red"></i>Full Name *
                                </label>
                                <input type="text" id="name" name="name" class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red" required>
                            </div>
                            <div>
                                <label for="email" class="block text-text-dark font-semibold mb-2">
                                    <i class="fas fa-envelope mr-2 text-primary-red"></i>Email Address *
                                </label>
                                <input type="email" id="email" name="email" class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red" required>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="phone" class="block text-text-dark font-semibold mb-2">
                                    <i class="fas fa-phone mr-2 text-primary-red"></i>Phone Number
                                </label>
                                <input type="tel" id="phone" name="phone" class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red" placeholder="****** 567 8900">
                            </div>
                            <div>
                                <label for="country" class="block text-text-dark font-semibold mb-2">
                                    <i class="fas fa-globe mr-2 text-primary-red"></i>Country/Region *
                                </label>
                                <input type="text" id="country" name="country" class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red" placeholder="Your country" required>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="company" class="block text-text-dark font-semibold mb-2">
                                <i class="fas fa-building mr-2 text-primary-red"></i>Company/Organization
                            </label>
                            <input type="text" id="company" name="company" class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red" placeholder="Your company name">
                        </div>

                        <!-- Product Interest Checkboxes -->
                        <div class="mb-4">
                            <label class="block text-text-dark font-semibold mb-3">
                                <i class="fas fa-drumstick-bite mr-2 text-primary-red"></i>Product Interest *
                            </label>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <label class="inline-flex items-center p-3 border-2 border-gray-200 rounded-lg hover:border-primary-red transition-colors cursor-pointer">
                                    <input type="checkbox" name="product_interest[]" value="Whole Chicken" class="form-checkbox h-5 w-5 text-primary-red border-2 border-gray-300 rounded focus:ring-primary-red">
                                    <span class="ml-3 text-gray-700 font-medium">Whole Chicken</span>
                                </label>
                                <label class="inline-flex items-center p-3 border-2 border-gray-200 rounded-lg hover:border-primary-red transition-colors cursor-pointer">
                                    <input type="checkbox" name="product_interest[]" value="Chicken Parts" class="form-checkbox h-5 w-5 text-primary-red border-2 border-gray-300 rounded focus:ring-primary-red">
                                    <span class="ml-3 text-gray-700 font-medium">Chicken Parts</span>
                                </label>
                                <label class="inline-flex items-center p-3 border-2 border-gray-200 rounded-lg hover:border-primary-red transition-colors cursor-pointer">
                                    <input type="checkbox" name="product_interest[]" value="Premium Cuts" class="form-checkbox h-5 w-5 text-primary-red border-2 border-gray-300 rounded focus:ring-primary-red">
                                    <span class="ml-3 text-gray-700 font-medium">Premium Cuts</span>
                                </label>
                                <label class="inline-flex items-center p-3 border-2 border-gray-200 rounded-lg hover:border-primary-red transition-colors cursor-pointer">
                                    <input type="checkbox" name="product_interest[]" value="Custom Processing" class="form-checkbox h-5 w-5 text-primary-red border-2 border-gray-300 rounded focus:ring-primary-red">
                                    <span class="ml-3 text-gray-700 font-medium">Custom Processing</span>
                                </label>
                            </div>
                        </div>

                        <div class="mb-6">
                            <label for="message" class="block text-text-dark font-semibold mb-2">
                                <i class="fas fa-comment mr-2 text-primary-red"></i>Requirements & Message *
                            </label>
                            <textarea id="message" name="message" rows="4" class="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-red focus:border-primary-red" placeholder="Please specify:&#10;• Product types and quantities needed&#10;• Delivery destination and timeline&#10;• Packaging requirements&#10;• Any special certifications needed" required></textarea>
                        </div>

                        <button type="submit" id="global-quote-submit-btn" class="w-full bg-primary-red hover:bg-secondary-red text-white font-bold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <i class="fas fa-paper-plane mr-2"></i>Submit Export Inquiry
                        </button>
                    </form>
                </div>
            </div>
        </div>

        @stack('scripts')

        @if(isset($siteSettings['copy_protection_enabled']) && $siteSettings['copy_protection_enabled'])
        <!-- Copy Protection Configuration -->
        <script>
            window.copyProtectionEnabled = true;
            window.copyProtectionWatermark = '{{ $siteSettings['company_name'] ?? 'PROTECTED CONTENT' }}';
        </script>
        <!-- Copy Protection Script -->
        <script src="{{ asset('js/copy-protection.js') }}"></script>
        @endif

        <!-- Global JavaScript -->
        <script>
            // Quote Modal Functionality - Define these outside DOMContentLoaded for global access
            const quoteModal = document.getElementById('quote-modal');
            const closeModal = document.getElementById('close-modal');

            function closeQuoteModal() {
                if (quoteModal) {
                    quoteModal.classList.add('hidden');
                    document.body.style.overflow = 'auto';

                    // Clear any messages and form errors when closing
                    const messageDiv = document.getElementById('global-quote-message');
                    const form = document.getElementById('global-quote-form');
                    if (messageDiv) messageDiv.className = 'hidden';
                    if (form) clearGlobalQuoteFormErrors(form);
                }
            }

            // Make openQuoteModal globally available
            window.openQuoteModal = function() {
                if (quoteModal) {
                    quoteModal.classList.remove('hidden');
                    document.body.style.overflow = 'hidden';
                }
            };

            // Copy Protection Function
            function initCopyProtection() {
                // Disable right-click context menu
                document.addEventListener('contextmenu', function(e) {
                    e.preventDefault();
                    return false;
                });

                // Disable text selection with mouse
                document.addEventListener('selectstart', function(e) {
                    e.preventDefault();
                    return false;
                });

                // Disable drag and drop
                document.addEventListener('dragstart', function(e) {
                    e.preventDefault();
                    return false;
                });

                // Disable keyboard shortcuts for copying
                document.addEventListener('keydown', function(e) {
                    // Disable Ctrl+A (Select All)
                    if (e.ctrlKey && e.keyCode === 65) {
                        e.preventDefault();
                        return false;
                    }
                    // Disable Ctrl+C (Copy)
                    if (e.ctrlKey && e.keyCode === 67) {
                        e.preventDefault();
                        return false;
                    }
                    // Disable Ctrl+V (Paste)
                    if (e.ctrlKey && e.keyCode === 86) {
                        e.preventDefault();
                        return false;
                    }
                    // Disable Ctrl+X (Cut)
                    if (e.ctrlKey && e.keyCode === 88) {
                        e.preventDefault();
                        return false;
                    }
                    // Disable Ctrl+S (Save)
                    if (e.ctrlKey && e.keyCode === 83) {
                        e.preventDefault();
                        return false;
                    }
                    // Disable Ctrl+P (Print)
                    if (e.ctrlKey && e.keyCode === 80) {
                        e.preventDefault();
                        return false;
                    }
                    // Disable F12 (Developer Tools)
                    if (e.keyCode === 123) {
                        e.preventDefault();
                        return false;
                    }
                    // Disable Ctrl+Shift+I (Developer Tools)
                    if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
                        e.preventDefault();
                        return false;
                    }
                    // Disable Ctrl+Shift+J (Console)
                    if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
                        e.preventDefault();
                        return false;
                    }
                    // Disable Ctrl+U (View Source)
                    if (e.ctrlKey && e.keyCode === 85) {
                        e.preventDefault();
                        return false;
                    }
                });

                // Disable image dragging
                const images = document.querySelectorAll('img');
                images.forEach(function(img) {
                    img.addEventListener('dragstart', function(e) {
                        e.preventDefault();
                        return false;
                    });
                    img.style.pointerEvents = 'none';
                });

                // Clear clipboard periodically (optional)
                setInterval(function() {
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        navigator.clipboard.writeText('').catch(function() {
                            // Ignore errors
                        });
                    }
                }, 1000);

                // Disable print screen (limited effectiveness)
                document.addEventListener('keyup', function(e) {
                    if (e.keyCode === 44) {
                        document.body.style.display = 'none';
                        setTimeout(function() {
                            document.body.style.display = 'block';
                        }, 100);
                    }
                });

                // Console warning
                console.clear();
                console.log('%cSTOP!', 'color: red; font-size: 50px; font-weight: bold;');
                console.log('%cThis is a browser feature intended for developers. Content on this website is protected by copyright law.', 'color: red; font-size: 16px;');
            }

            document.addEventListener('DOMContentLoaded', function() {
                // Mobile Menu Toggle
                const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
                const mobileMenu = document.getElementById('mobile-menu');

                if (mobileMenuToggle && mobileMenu) {
                    mobileMenuToggle.addEventListener('click', function() {
                        mobileMenu.classList.toggle('hidden');
                    });
                }

                // Close modal event listeners
                if (closeModal) {
                    closeModal.addEventListener('click', closeQuoteModal);
                }

                // Close modal when clicking outside
                if (quoteModal) {
                    quoteModal.addEventListener('click', function(e) {
                        if (e.target === quoteModal) {
                            closeQuoteModal();
                        }
                    });
                }

                // Close modal with Escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        closeQuoteModal();
                    }
                });

                // Initialize global quote form AJAX
                initGlobalQuoteForm();

                // WhatsApp Floating Button Enhancement
                const whatsappFloat = document.getElementById('whatsapp-float');
                if (whatsappFloat) {
                    // Add entrance animation after page load
                    setTimeout(() => {
                        whatsappFloat.classList.add('animate-bounce');
                        setTimeout(() => {
                            whatsappFloat.classList.remove('animate-bounce');
                        }, 2000);
                    }, 1000);

                    // Add click tracking (optional)
                    const whatsappLink = whatsappFloat.querySelector('a');
                    if (whatsappLink) {
                        whatsappLink.addEventListener('click', function() {
                            // Track WhatsApp button clicks (you can integrate with analytics)
                            console.log('WhatsApp button clicked');

                            // Optional: Show a brief success message
                            const tooltip = whatsappFloat.querySelector('.absolute.left-16');
                            if (tooltip) {
                                const originalText = tooltip.textContent;
                                tooltip.textContent = 'Opening WhatsApp...';
                                setTimeout(() => {
                                    tooltip.textContent = originalText;
                                }, 2000);
                            }
                        });
                    }

                    // Hide/show on scroll (modern UX pattern)
                    let lastScrollTop = 0;
                    window.addEventListener('scroll', function() {
                        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                        if (scrollTop > lastScrollTop && scrollTop > 100) {
                            // Scrolling down - hide button
                            whatsappFloat.style.transform = 'translateX(-100px)';
                            whatsappFloat.style.opacity = '0.7';
                        } else {
                            // Scrolling up - show button
                            whatsappFloat.style.transform = 'translateX(0)';
                            whatsappFloat.style.opacity = '1';
                        }

                        lastScrollTop = scrollTop;
                    });
                }

                // Newsletter AJAX functionality
                initNewsletterForms();

                // Copy Protection
                @if(isset($siteSettings['copy_protection_enabled']) && $siteSettings['copy_protection_enabled'])
                initCopyProtection();
                @endif
            });

            // Newsletter AJAX Form Handler
            function initNewsletterForms() {
                // Handle Footer Newsletter Form
                const footerForm = document.getElementById('footer-newsletter-form');
                if (footerForm) {
                    footerForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        handleNewsletterSubmission(this, 'footer');
                    });
                }

                // Handle Blog Newsletter Form
                const blogForm = document.getElementById('blog-newsletter-form');
                if (blogForm) {
                    blogForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        handleNewsletterSubmission(this, 'blog');
                    });
                }

                // Handle Unsubscribe Form
                const unsubscribeForm = document.getElementById('unsubscribe-form');
                if (unsubscribeForm) {
                    unsubscribeForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        handleUnsubscribeSubmission(this);
                    });
                }
            }

            // Newsletter Subscription Handler
            function handleNewsletterSubmission(form, type) {
                const emailInput = form.querySelector('input[name="email"]');
                const submitBtn = form.querySelector('button[type="submit"]');
                const messageDiv = form.querySelector(`#${type}-newsletter-message`);

                // Clear previous messages
                messageDiv.className = 'hidden';
                emailInput.classList.remove('border-red-500', 'border-green-500');

                // Validate email
                const email = emailInput.value.trim();
                if (!email || !isValidEmail(email)) {
                    showMessage(messageDiv, 'Please enter a valid email address.', 'error');
                    emailInput.classList.add('border-red-500');
                    return;
                }

                // Show loading state
                const originalBtnContent = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Subscribing...';
                submitBtn.disabled = true;

                // Prepare form data
                const formData = new FormData(form);

                // Send AJAX request
                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                                       form.querySelector('input[name="_token"]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage(messageDiv, data.message, 'success');
                        emailInput.classList.add('border-green-500');
                        emailInput.value = ''; // Clear the form

                        // Show success alert
                        alert('✅ Success!\n\n' + data.message);
                    } else {
                        showMessage(messageDiv, data.message || 'An error occurred. Please try again.', 'error');
                        emailInput.classList.add('border-red-500');

                        // Show error alert
                        alert('❌ Error!\n\n' + (data.message || 'An error occurred. Please try again.'));
                    }
                })
                .catch(error => {
                    console.error('Newsletter subscription error:', error);
                    showMessage(messageDiv, 'An error occurred. Please try again.', 'error');
                    emailInput.classList.add('border-red-500');

                    // Show error alert
                    alert('❌ Error!\n\nAn error occurred while subscribing to the newsletter. Please try again.');
                })
                .finally(() => {
                    // Restore button state
                    submitBtn.innerHTML = originalBtnContent;
                    submitBtn.disabled = false;
                });
            }

            // Unsubscribe Handler
            function handleUnsubscribeSubmission(form) {
                const submitBtn = form.querySelector('button[type="submit"]');
                const messageDiv = form.querySelector('#unsubscribe-message');

                // Show loading state
                const originalBtnContent = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                submitBtn.disabled = true;

                // Prepare form data
                const formData = new FormData(form);

                // Send AJAX request
                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                                       form.querySelector('input[name="_token"]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage(messageDiv, data.message, 'success');
                        // Hide the form after successful unsubscribe
                        setTimeout(() => {
                            if (data.redirect) {
                                window.location.href = data.redirect;
                            }
                        }, 2000);
                    } else {
                        showMessage(messageDiv, data.message || 'An error occurred. Please try again.', 'error');
                    }
                })
                .catch(error => {
                    console.error('Unsubscribe error:', error);
                    showMessage(messageDiv, 'An error occurred. Please try again.', 'error');
                })
                .finally(() => {
                    // Restore button state
                    submitBtn.innerHTML = originalBtnContent;
                    submitBtn.disabled = false;
                });
            }

            // Utility Functions
            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            function showMessage(messageDiv, message, type) {
                const isError = type === 'error';
                const bgColor = isError ? 'bg-red-100' : 'bg-green-100';
                const textColor = isError ? 'text-red-800' : 'text-green-800';
                const borderColor = isError ? 'border-red-200' : 'border-green-200';
                const icon = isError ? 'fas fa-exclamation-circle' : 'fas fa-check-circle';

                messageDiv.className = `${bgColor} ${textColor} border ${borderColor} rounded-lg p-3 text-sm`;
                messageDiv.innerHTML = `
                    <div class="flex items-center">
                        <i class="${icon} mr-2"></i>
                        <span>${message}</span>
                    </div>
                `;

                // Auto-hide success messages after 5 seconds
                if (!isError) {
                    setTimeout(() => {
                        messageDiv.className = 'hidden';
                    }, 5000);
                }
            }

            // Global Quote Form AJAX Handler
            function initGlobalQuoteForm() {
                const globalQuoteForm = document.getElementById('global-quote-form');
                if (globalQuoteForm) {
                    globalQuoteForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        handleGlobalQuoteSubmission(this);
                    });
                }
            }

            // Global Quote Form Submission Handler
            function handleGlobalQuoteSubmission(form) {
                const submitBtn = form.querySelector('#global-quote-submit-btn');
                const messageDiv = document.getElementById('global-quote-message');

                // Clear previous messages and errors
                messageDiv.className = 'hidden';
                clearGlobalQuoteFormErrors(form);

                // Validate required fields
                const requiredFields = form.querySelectorAll('[required]');
                let hasErrors = false;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        showGlobalQuoteFieldError(field, 'This field is required.');
                        hasErrors = true;
                    } else if (field.type === 'email' && !isValidEmail(field.value)) {
                        showGlobalQuoteFieldError(field, 'Please enter a valid email address.');
                        hasErrors = true;
                    }
                });

                if (hasErrors) {
                    showGlobalQuoteMessage(messageDiv, 'Please correct the errors below.', 'error');
                    return;
                }

                // Show loading state
                const originalBtnContent = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending Request...';
                submitBtn.disabled = true;

                // Prepare form data
                const formData = new FormData(form);

                // Send AJAX request
                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                                       form.querySelector('input[name="_token"]').value
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => Promise.reject(data));
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showGlobalQuoteMessage(messageDiv, data.message, 'success');
                        form.reset(); // Clear the form
                        clearGlobalQuoteFormErrors(form);

                        // Show success alert
                        alert('✅ Success!\n\n' + data.message);

                        // Auto-close modal after alert
                        closeQuoteModal();
                    } else {
                        showGlobalQuoteMessage(messageDiv, data.message || 'An error occurred. Please try again.', 'error');

                        // Show error alert
                        alert('❌ Error!\n\n' + (data.message || 'An error occurred. Please try again.'));
                    }
                })
                .catch(error => {
                    console.error('Global quote form error:', error);

                    // Handle validation errors
                    if (error.errors) {
                        Object.keys(error.errors).forEach(field => {
                            const fieldElement = form.querySelector(`[name="${field}"]`);
                            if (fieldElement) {
                                showGlobalQuoteFieldError(fieldElement, error.errors[field][0]);
                            }
                        });
                        showGlobalQuoteMessage(messageDiv, 'Please correct the errors below.', 'error');

                        // Show validation error alert
                        alert('❌ Validation Error!\n\nPlease correct the errors in the form and try again.');
                    } else {
                        showGlobalQuoteMessage(messageDiv, error.message || 'An error occurred. Please try again.', 'error');

                        // Show general error alert
                        alert('❌ Error!\n\n' + (error.message || 'An error occurred. Please try again.'));
                    }
                })
                .finally(() => {
                    // Restore button state
                    submitBtn.innerHTML = originalBtnContent;
                    submitBtn.disabled = false;
                });
            }

            // Global Quote Form Utility Functions
            function showGlobalQuoteMessage(messageDiv, message, type) {
                const isError = type === 'error';
                const bgColor = isError ? 'bg-red-100' : 'bg-green-100';
                const textColor = isError ? 'text-red-800' : 'text-green-800';
                const borderColor = isError ? 'border-red-200' : 'border-green-200';
                const icon = isError ? 'fas fa-exclamation-circle' : 'fas fa-check-circle';

                messageDiv.className = `${bgColor} ${textColor} border ${borderColor} rounded-lg p-3 mb-4`;
                messageDiv.innerHTML = `
                    <div class="flex items-center">
                        <i class="${icon} mr-2"></i>
                        <span class="font-medium">${message}</span>
                    </div>
                `;
            }

            function showGlobalQuoteFieldError(field, message) {
                // Add error styling to field
                field.classList.add('border-red-500');
                field.classList.remove('border-gray-300');

                // Remove existing error message
                const existingError = field.parentNode.querySelector('.global-quote-field-error');
                if (existingError) {
                    existingError.remove();
                }

                // Add error message
                const errorDiv = document.createElement('p');
                errorDiv.className = 'text-red-500 text-sm mt-1 global-quote-field-error';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
            }

            function clearGlobalQuoteFormErrors(form) {
                // Remove error styling from all fields
                const fields = form.querySelectorAll('input, select, textarea');
                fields.forEach(field => {
                    field.classList.remove('border-red-500');
                    field.classList.add('border-gray-300');
                });

                // Remove all error messages
                const errorMessages = form.querySelectorAll('.global-quote-field-error');
                errorMessages.forEach(error => error.remove());
            }
        </script>
    </body>
</html>
