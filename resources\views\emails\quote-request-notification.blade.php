@php
use App\Services\CompanyInfoService;
@endphp

@component('mail::message')
@if(CompanyInfoService::logo())
<img src="{{ asset('storage/' . CompanyInfoService::logo()) }}" alt="{{ CompanyInfoService::name() }}" style="max-height: 50px; margin-bottom: 20px;">
@endif

# New Export Inquiry Received
@if(CompanyInfoService::tagline())
{{ CompanyInfoService::tagline() }}
@endif

**Export Inquiry Details:**

@component('mail::panel', ['style' => 'background-color: #f9f9f9; border: 1px solid #ddd; padding: 15px;'])
## Customer Information
- **Name:** {{ $quoteRequest['name'] }}
- **Email:** {{ $quoteRequest['email'] }}
- **Phone:** {{ $quoteRequest['phone'] ?? 'Not provided' }}
- **Company:** {{ $quoteRequest['company'] ?? 'Not specified' }}
- **Country/Region:** {{ $quoteRequest['country'] ?? 'Not specified' }}
@if(isset($quoteRequest['inquiry_type']) && $quoteRequest['inquiry_type'])
- **Inquiry Type:** {{ ucfirst(str_replace('_', ' ', $quoteRequest['inquiry_type'])) }}
@endif

## Product Interest & Requirements
@if(isset($quoteRequest['product_interest']) && is_array($quoteRequest['product_interest']) && count($quoteRequest['product_interest']) > 0)
- **Poultry Products of Interest:** {{ implode(', ', $quoteRequest['product_interest']) }}
@else
- **Poultry Products of Interest:** Not specified
@endif

@if(isset($quoteRequest['product_id']) && $quoteRequest['product_id'])
@php
    $product = \App\Models\Product::find($quoteRequest['product_id']);
@endphp
- **Specific Product:** {{ $product?->name ?? 'Product not found' }}
- **Category:** {{ optional($product->category)->name ?? 'N/A' }}
@endif

- **Estimated Quantity:** {{ $quoteRequest['quantity'] ?? 'To be discussed' }}

@if(isset($quoteRequest['delivery_destination']) && $quoteRequest['delivery_destination'])
- **Delivery Destination:** {{ $quoteRequest['delivery_destination'] }}
@endif

@if(isset($quoteRequest['packaging_requirements']) && $quoteRequest['packaging_requirements'])
- **Packaging Requirements:** {{ $quoteRequest['packaging_requirements'] }}
@endif

@if(isset($quoteRequest['special_certifications']) && $quoteRequest['special_certifications'])
- **Special Certifications:** {{ $quoteRequest['special_certifications'] }}
@endif

@if(isset($quoteRequest['preferred_contact_method']) && $quoteRequest['preferred_contact_method'])
- **Preferred Contact Method:** {{ ucfirst($quoteRequest['preferred_contact_method']) }}
@endif

@if(isset($quoteRequest['timeline']) && $quoteRequest['timeline'])
- **Required Timeline:** {{ ucfirst($quoteRequest['timeline']) }}
@endif

## Customer Message
{{ $quoteRequest['message'] }}
@endcomponent

@component('mail::button', ['url' => route('admin.quote-requests.index'), 'color' => 'red'])
View Export Inquiry in Dashboard
@endcomponent

**Next Steps:**
1. Review the customer's requirements carefully
2. Prepare a detailed quote with pricing and specifications
3. Include relevant certifications (Halal, HACCP, etc.)
4. Provide export documentation requirements
5. Contact the customer within 24 hours

Thank you for using {{ CompanyInfoService::name() }}'s Export Inquiry System.

Best regards,<br>
**{{ CompanyInfoService::name() }}**<br>
*Premium Halal Poultry Products from Eswatini*
@if(CompanyInfoService::phone())<br>📞 {{ CompanyInfoService::phone() }}@endif
@if(CompanyInfoService::email())<br>📧 {{ CompanyInfoService::email() }}@endif
@if(CompanyInfoService::address())<br>📍 {{ CompanyInfoService::address() }}@endif

<small style="color: #718096;">This is an automated export inquiry notification. Please respond to the customer directly at their provided email address.</small>
@endcomponent
