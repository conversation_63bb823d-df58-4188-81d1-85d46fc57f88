<?php

use App\Models\SiteSetting;

if (!function_exists('site_image')) {
    /**
     * Get a site image URL with fallback
     *
     * @param string $key
     * @param string|null $default
     * @return string|null
     */
    function site_image($key, $default = null) {
        $imagePath = SiteSetting::get($key);
        if ($imagePath && file_exists(storage_path('app/public/' . $imagePath))) {
            return asset('storage/' . $imagePath);
        }
        return $default;
    }
}

if (!function_exists('blog_image')) {
    /**
     * Get blog image or default
     *
     * @param string|null $blogImage
     * @return string
     */
    function blog_image($blogImage = null) {
        if ($blogImage && file_exists(storage_path('app/public/' . $blogImage))) {
            return asset('storage/' . $blogImage);
        }
        return site_image('blog_default_header', asset('images/default-blog.svg'));
    }
}

if (!function_exists('testimonial_avatar')) {
    /**
     * Get testimonial avatar or default
     *
     * @param string|null $avatarImage
     * @return string
     */
    function testimonial_avatar($avatarImage = null) {
        if ($avatarImage && file_exists(storage_path('app/public/' . $avatarImage))) {
            return asset('storage/' . $avatarImage);
        }
        return site_image('testimonials_default_avatar', asset('images/default-avatar.svg'));
    }
}
