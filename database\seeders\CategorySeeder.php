<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Fresh Poultry',
                'slug' => 'fresh-poultry',
                'description' => 'Fresh poultry products including whole chickens and premium cuts',
                'parent_id' => null,
            ],
            [
                'name' => 'Processed Parts',
                'slug' => 'processed-parts',
                'description' => 'Processed poultry parts including feet, gizzards, and necks',
                'parent_id' => null,
            ],
            [
                'name' => 'Premium Cuts',
                'slug' => 'premium-cuts',
                'description' => 'Premium chicken cuts including breast fillets and drumsticks',
                'parent_id' => null,
            ],
            [
                'name' => 'Whole Chicken',
                'slug' => 'whole-chicken',
                'description' => 'Complete whole chickens in various sizes',
                'parent_id' => 1, // Fresh Poultry
            ],
            [
                'name' => 'Chicken Parts',
                'slug' => 'chicken-parts',
                'description' => 'Individual chicken parts and cuts',
                'parent_id' => 1, // Fresh Poultry
            ],
            [
                'name' => 'Breast Cuts',
                'slug' => 'breast-cuts',
                'description' => 'Chicken breast cuts - skin-on and skin-off options',
                'parent_id' => 3, // Premium Cuts
            ],
            [
                'name' => 'Leg Cuts',
                'slug' => 'leg-cuts',
                'description' => 'Chicken leg quarters and drumsticks',
                'parent_id' => 3, // Premium Cuts
            ],
            [
                'name' => 'Specialty Parts',
                'slug' => 'specialty-parts',
                'description' => 'Specialty parts including feet, gizzards, and necks',
                'parent_id' => 2, // Processed Parts
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }
    }
}
