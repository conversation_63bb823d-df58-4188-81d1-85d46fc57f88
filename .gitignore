# Production deployment .gitignore
# Excludes development files, databases, logs, and build artifacts

# Development dependencies
/node_modules
/vendor

# Build artifacts
/public/build
/public/hot

# Environment files
.env*

# Database files
/database/database.sqlite
*.db
*.sqlite

# Development directories
/planning
/tests
/.phpunit.cache
.phpunit.result.cache
phpunit.xml

# IDE and editor files
/.fleet
/.idea
/.vscode

# Storage and cache
/storage/logs
/storage/*.key



# Development configs
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log

# Public storage symlink (created during deployment)
/public/storage

# Compiled assets (built during deployment)
/public/css
/public/js
