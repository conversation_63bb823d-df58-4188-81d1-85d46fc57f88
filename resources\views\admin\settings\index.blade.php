@extends('admin.layouts.app')

@section('header', 'Site Settings')

@section('content')
<div class="bg-white rounded-lg shadow-md">
    <div class="p-6 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-800">
            <i class="fas fa-cog mr-2 text-primary-color"></i>
            Site Configuration
        </h2>
        <p class="text-gray-600 mt-1">Manage your website's global settings, branding, and image management.</p>
    </div>

    <!-- Tab Navigation -->
    <div class="border-b border-gray-200">
        <nav class="flex space-x-8 px-6" aria-label="Tabs">
            <button type="button" class="tab-button active py-4 px-1 border-b-2 border-primary-color font-medium text-sm text-primary-color" data-tab="general">
                <i class="fas fa-cog mr-2"></i>
                General Settings
            </button>
            <button type="button" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="home-images">
                <i class="fas fa-home mr-2"></i>
                Home Page Images
            </button>
            <button type="button" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="about-images">
                <i class="fas fa-info-circle mr-2"></i>
                About Page Images
            </button>
            <button type="button" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="contact-images">
                <i class="fas fa-envelope mr-2"></i>
                Contact Page Images
            </button>
            <button type="button" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="content-images">
                <i class="fas fa-images mr-2"></i>
                Content Images
            </button>
        </nav>
    </div>

    <form action="{{ route('admin.settings.update') }}" method="POST" enctype="multipart/form-data" class="p-6">
        @csrf
        @method('PUT')

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- General Settings Tab -->
            <div id="general-tab" class="tab-pane active">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Left Column - Basic Settings -->
            <div class="space-y-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                        Basic Information
                    </h3>

                    <!-- Site Title -->
                    <div class="mb-4">
                        <label for="site_title" class="block text-sm font-medium text-gray-700 mb-2">Site Title</label>
                        <input type="text" name="site_title" id="site_title"
                               value="{{ old('site_title', $settings['site_title']->value ?? '') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent"
                               required>
                        @error('site_title')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Company Name -->
                    <div class="mb-4">
                        <label for="company_name" class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                        <input type="text" name="company_name" id="company_name"
                               value="{{ old('company_name', $settings['company_name']->value ?? '') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent"
                               required>
                        @error('company_name')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Company Tagline -->
                    <div class="mb-4">
                        <label for="company_tagline" class="block text-sm font-medium text-gray-700 mb-2">Company Tagline</label>
                        <input type="text" name="company_tagline" id="company_tagline"
                               value="{{ old('company_tagline', $settings['company_tagline']->value ?? '') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        @error('company_tagline')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Currency Settings -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-money-bill-wave mr-2 text-green-500"></i>
                        Currency Settings
                    </h3>

                    <div class="grid grid-cols-2 gap-4">
                        <!-- Currency Symbol -->
                        <div>
                            <label for="currency_symbol" class="block text-sm font-medium text-gray-700 mb-2">Currency Symbol</label>
                            <input type="text" name="currency_symbol" id="currency_symbol"
                                   value="{{ old('currency_symbol', $settings['currency_symbol']->value ?? 'R') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent"
                                   required>
                            @error('currency_symbol')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Currency Code -->
                        <div>
                            <label for="currency_code" class="block text-sm font-medium text-gray-700 mb-2">Currency Code</label>
                            <input type="text" name="currency_code" id="currency_code"
                                   value="{{ old('currency_code', $settings['currency_code']->value ?? 'ZAR') }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent"
                                   required>
                            @error('currency_code')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Delivery Settings -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-truck mr-2 text-blue-500"></i>
                        Delivery Settings
                    </h3>

                    <div class="flex items-center">
                        <input type="checkbox" name="delivery_nationwide" id="delivery_nationwide"
                               value="1" {{ old('delivery_nationwide', $settings['delivery_nationwide']->value ?? false) ? 'checked' : '' }}
                               class="h-4 w-4 text-primary-color focus:ring-primary-color border-gray-300 rounded">
                        <label for="delivery_nationwide" class="ml-2 block text-sm text-gray-700">
                            We deliver nationwide
                        </label>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-shield-alt mr-2 text-red-500"></i>
                        Content Protection
                    </h3>

                    <div class="flex items-center">
                        <input type="checkbox" name="copy_protection_enabled" id="copy_protection_enabled"
                               value="1" {{ old('copy_protection_enabled', $settings['copy_protection_enabled']->value ?? false) ? 'checked' : '' }}
                               class="h-4 w-4 text-primary-color focus:ring-primary-color border-gray-300 rounded">
                        <label for="copy_protection_enabled" class="ml-2 block text-sm text-gray-700">
                            Enable content copy protection
                        </label>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">
                        When enabled, prevents text selection, right-click, and keyboard shortcuts for copying content
                    </p>
                </div>
            </div>

            <!-- Right Column - Contact & Media -->
            <div class="space-y-6">
                <!-- Logo & Favicon -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-image mr-2 text-purple-500"></i>
                        Branding
                    </h3>

                    <!-- Site Logo -->
                    <div class="mb-4">
                        <label for="site_logo" class="block text-sm font-medium text-gray-700 mb-2">Site Logo</label>
                        @if(isset($settings['site_logo']) && $settings['site_logo']->value)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $settings['site_logo']->value) }}" alt="Current Logo" class="h-16 object-contain">
                                <p class="text-xs text-gray-500 mt-1">Current logo</p>
                            </div>
                        @endif
                        <input type="file" name="site_logo" id="site_logo" accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        <p class="text-xs text-gray-500 mt-1">Recommended size: 300x100px. Formats: JPG, PNG, SVG</p>
                        @error('site_logo')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Site Favicon -->
                    <div class="mb-4">
                        <label for="site_favicon" class="block text-sm font-medium text-gray-700 mb-2">Site Favicon</label>
                        @if(isset($settings['site_favicon']) && $settings['site_favicon']->value)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $settings['site_favicon']->value) }}" alt="Current Favicon" class="h-8 w-8 object-contain">
                                <p class="text-xs text-gray-500 mt-1">Current favicon</p>
                            </div>
                        @endif
                        <input type="file" name="site_favicon" id="site_favicon" accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        <p class="text-xs text-gray-500 mt-1">Will be converted to .ico format. Recommended: 32x32px square image</p>
                        @error('site_favicon')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-address-book mr-2 text-green-500"></i>
                        Contact Information
                    </h3>

                    <!-- Company Phone -->
                    <div class="mb-4">
                        <label for="company_phone" class="block text-sm font-medium text-gray-700 mb-2">Company Phone</label>
                        <input type="text" name="company_phone" id="company_phone"
                               value="{{ old('company_phone', $settings['company_phone']->value ?? '') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        @error('company_phone')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Company Email -->
                    <div class="mb-4">
                        <label for="company_email" class="block text-sm font-medium text-gray-700 mb-2">Company Email</label>
                        <input type="email" name="company_email" id="company_email"
                               value="{{ old('company_email', $settings['company_email']->value ?? '') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        @error('company_email')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Company Address -->
                    <div class="mb-4">
                        <label for="company_address" class="block text-sm font-medium text-gray-700 mb-2">Company Address</label>
                        <textarea name="company_address" id="company_address" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">{{ old('company_address', $settings['company_address']->value ?? '') }}</textarea>
                        @error('company_address')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- WhatsApp Settings -->
                    <div class="mb-4">
                        <div class="flex items-center mb-3">
                            <input type="checkbox" name="whatsapp_enabled" id="whatsapp_enabled"
                                   value="1" {{ old('whatsapp_enabled', $settings['whatsapp_enabled']->value ?? false) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-color focus:ring-primary-color border-gray-300 rounded">
                            <label for="whatsapp_enabled" class="ml-2 block text-sm text-gray-700">
                                Enable WhatsApp floating button
                            </label>
                        </div>

                        <label for="whatsapp_number" class="block text-sm font-medium text-gray-700 mb-2">
                            WhatsApp Number
                        </label>
                        <input type="text" name="whatsapp_number" id="whatsapp_number"
                               value="{{ old('whatsapp_number', $settings['whatsapp_number']->value ?? '') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        <p class="text-xs text-gray-500 mt-1">Include country code (e.g., +268 for Eswatini)</p>
                        @error('whatsapp_number')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- About Us Content -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-info mr-2 text-blue-500"></i>
                        About Us Content
                    </h3>

                    <div class="mb-4">
                        <label for="about_us_content" class="block text-sm font-medium text-gray-700 mb-2">About Us Description</label>
                        <textarea name="about_us_content" id="about_us_content" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent"
                                  placeholder="Brief description about your company for homepage and about page">{{ old('about_us_content', $settings['about_us_content']->value ?? '') }}</textarea>
                        @error('about_us_content')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Notification Settings -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">
                        <i class="fas fa-envelope mr-2 text-yellow-500"></i>
                        Form Notification Settings
                    </h3>

                    <!-- Contact Form Notification Emails -->
                    <div class="mb-4">
                        <label for="contact_form_emails" class="block text-sm font-medium text-gray-700 mb-2">
                            Contact Form Notification Emails
                        </label>
                        <input type="text" name="contact_form_emails" id="contact_form_emails"
                               value="{{ old('contact_form_emails', $settings['contact_form_emails']->value ?? '') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent"
                               placeholder="<EMAIL>, <EMAIL>">
                        <p class="text-xs text-gray-500 mt-1">Separate multiple email addresses with commas</p>
                        @error('contact_form_emails')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Quote Request Notification Emails -->
                    <div class="mb-4">
                        <label for="quote_request_emails" class="block text-sm font-medium text-gray-700 mb-2">
                            Quote Request Notification Emails
                        </label>
                        <input type="text" name="quote_request_emails" id="quote_request_emails"
                               value="{{ old('quote_request_emails', $settings['quote_request_emails']->value ?? '') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent"
                               placeholder="<EMAIL>, <EMAIL>">
                        <p class="text-xs text-gray-500 mt-1">Separate multiple email addresses with commas</p>
                        @error('quote_request_emails')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
                </div>
            </div>

            <!-- Home Page Images Tab -->
            <div id="home-images-tab" class="tab-pane hidden">
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">
                        <i class="fas fa-home mr-2 text-green-500"></i>
                        Home Page Images
                    </h3>
                    <p class="text-gray-600 mb-6">Upload images for various sections of your home page. Images will be automatically optimized and converted to WebP format for better performance.</p>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Hero Slider Images -->
                <div class="space-y-6">
                    <h4 class="text-md font-semibold text-gray-700 border-b border-gray-300 pb-2">
                        <i class="fas fa-sliders-h mr-2 text-blue-500"></i>
                        Hero Slider Images
                    </h4>
                    <p class="text-sm text-gray-600 mb-4">These images are used when no dynamic sliders are created. Recommended size: 1920x800px</p>

                    <!-- Hero Slide 1 -->
                    <div class="mb-4">
                        <label for="hero_slide_1_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Hero Slide 1 - Premium Products
                        </label>
                        @if(isset($settings['hero_slide_1_image']) && $settings['hero_slide_1_image']->value)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $settings['hero_slide_1_image']->value) }}"
                                     alt="Hero Slide 1"
                                     class="w-32 h-20 object-cover rounded border">
                            </div>
                        @endif
                        <input type="file" name="hero_slide_1_image" id="hero_slide_1_image"
                               accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        @error('hero_slide_1_image')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Hero Slide 2 -->
                    <div class="mb-4">
                        <label for="hero_slide_2_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Hero Slide 2 - Export Quality
                        </label>
                        @if(isset($settings['hero_slide_2_image']) && $settings['hero_slide_2_image']->value)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $settings['hero_slide_2_image']->value) }}"
                                     alt="Hero Slide 2"
                                     class="w-32 h-20 object-cover rounded border">
                            </div>
                        @endif
                        <input type="file" name="hero_slide_2_image" id="hero_slide_2_image"
                               accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        @error('hero_slide_2_image')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Hero Slide 3 -->
                    <div class="mb-4">
                        <label for="hero_slide_3_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Hero Slide 3 - Quality Standards
                        </label>
                        @if(isset($settings['hero_slide_3_image']) && $settings['hero_slide_3_image']->value)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $settings['hero_slide_3_image']->value) }}"
                                     alt="Hero Slide 3"
                                     class="w-32 h-20 object-cover rounded border">
                            </div>
                        @endif
                        <input type="file" name="hero_slide_3_image" id="hero_slide_3_image"
                               accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        @error('hero_slide_3_image')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Section Images -->
                <div class="space-y-6">
                    <h4 class="text-md font-semibold text-gray-700 border-b border-gray-300 pb-2">
                        <i class="fas fa-building mr-2 text-orange-500"></i>
                        Section Images
                    </h4>
                    <p class="text-sm text-gray-600 mb-4">Images for various sections on the home page</p>

                    <!-- Company Overview Image -->
                    <div class="mb-4">
                        <label for="company_overview_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Company Overview Image
                        </label>
                        <p class="text-xs text-gray-500 mb-2">Recommended size: 600x400px</p>
                        @if(isset($settings['company_overview_image']) && $settings['company_overview_image']->value)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $settings['company_overview_image']->value) }}"
                                     alt="Company Overview"
                                     class="w-32 h-20 object-cover rounded border">
                            </div>
                        @endif
                        <input type="file" name="company_overview_image" id="company_overview_image"
                               accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        @error('company_overview_image')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- About Section Image -->
                    <div class="mb-4">
                        <label for="about_section_image" class="block text-sm font-medium text-gray-700 mb-2">
                            About Section Image
                        </label>
                        <p class="text-xs text-gray-500 mb-2">Recommended size: 800x600px</p>
                        @if(isset($settings['about_section_image']) && $settings['about_section_image']->value)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $settings['about_section_image']->value) }}"
                                     alt="About Section"
                                     class="w-32 h-20 object-cover rounded border">
                            </div>
                        @endif
                        <input type="file" name="about_section_image" id="about_section_image"
                               accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        @error('about_section_image')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Facility Image -->
                    <div class="mb-4">
                        <label for="facility_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Facility Image
                        </label>
                        <p class="text-xs text-gray-500 mb-2">Recommended size: 800x600px</p>
                        @if(isset($settings['facility_image']) && $settings['facility_image']->value)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $settings['facility_image']->value) }}"
                                     alt="Facility"
                                     class="w-32 h-20 object-cover rounded border">
                            </div>
                        @endif
                        <input type="file" name="facility_image" id="facility_image"
                               accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        @error('facility_image')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Team Image -->
                    <div class="mb-4">
                        <label for="team_image" class="block text-sm font-medium text-gray-700 mb-2">
                            Team Image
                        </label>
                        <p class="text-xs text-gray-500 mb-2">Recommended size: 600x400px</p>
                        @if(isset($settings['team_image']) && $settings['team_image']->value)
                            <div class="mb-2">
                                <img src="{{ asset('storage/' . $settings['team_image']->value) }}"
                                     alt="Team"
                                     class="w-32 h-20 object-cover rounded border">
                            </div>
                        @endif
                        <input type="file" name="team_image" id="team_image"
                               accept="image/*"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                        @error('team_image')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>
                </div>
            </div>

            <!-- About Page Images Tab -->
            <div id="about-images-tab" class="tab-pane hidden">
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">
                        <i class="fas fa-info-circle mr-2 text-blue-500"></i>
                        About Page Images
                    </h3>
                    <p class="text-gray-600 mb-6">Upload images for various sections of your About page. Images will be automatically optimized and converted to WebP format.</p>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- About Hero Image -->
                        <div class="space-y-6">
                            <h4 class="text-md font-semibold text-gray-700 border-b border-gray-300 pb-2">
                                <i class="fas fa-image mr-2 text-blue-500"></i>
                                Hero & Main Images
                            </h4>

                            <!-- About Hero Image -->
                            <div class="mb-4">
                                <label for="about_hero_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    About Page Hero Image
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Recommended size: 1920x800px</p>
                                @if(isset($settings['about_hero_image']) && $settings['about_hero_image']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['about_hero_image']->value) }}"
                                             alt="About Hero"
                                             class="w-32 h-20 object-cover rounded border">
                                    </div>
                                @endif
                                <input type="file" name="about_hero_image" id="about_hero_image"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('about_hero_image')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- About Team Image -->
                            <div class="mb-4">
                                <label for="about_team_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Team Image
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Recommended size: 800x600px</p>
                                @if(isset($settings['about_team_image']) && $settings['about_team_image']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['about_team_image']->value) }}"
                                             alt="Team"
                                             class="w-32 h-20 object-cover rounded border">
                                    </div>
                                @endif
                                <input type="file" name="about_team_image" id="about_team_image"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('about_team_image')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Facility Tour Image -->
                            <div class="mb-4">
                                <label for="about_facility_tour_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Facility Tour Image
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Recommended size: 800x600px</p>
                                @if(isset($settings['about_facility_tour_image']) && $settings['about_facility_tour_image']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['about_facility_tour_image']->value) }}"
                                             alt="Facility Tour"
                                             class="w-32 h-20 object-cover rounded border">
                                    </div>
                                @endif
                                <input type="file" name="about_facility_tour_image" id="about_facility_tour_image"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('about_facility_tour_image')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Mission, Vision, Values Images -->
                        <div class="space-y-6">
                            <h4 class="text-md font-semibold text-gray-700 border-b border-gray-300 pb-2">
                                <i class="fas fa-bullseye mr-2 text-orange-500"></i>
                                Mission, Vision & Values
                            </h4>

                            <!-- Mission Image -->
                            <div class="mb-4">
                                <label for="about_mission_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Mission Image
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Recommended size: 600x400px</p>
                                @if(isset($settings['about_mission_image']) && $settings['about_mission_image']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['about_mission_image']->value) }}"
                                             alt="Mission"
                                             class="w-32 h-20 object-cover rounded border">
                                    </div>
                                @endif
                                <input type="file" name="about_mission_image" id="about_mission_image"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('about_mission_image')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Vision Image -->
                            <div class="mb-4">
                                <label for="about_vision_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Vision Image
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Recommended size: 600x400px</p>
                                @if(isset($settings['about_vision_image']) && $settings['about_vision_image']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['about_vision_image']->value) }}"
                                             alt="Vision"
                                             class="w-32 h-20 object-cover rounded border">
                                    </div>
                                @endif
                                <input type="file" name="about_vision_image" id="about_vision_image"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('about_vision_image')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Values Image -->
                            <div class="mb-4">
                                <label for="about_values_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Values Image
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Recommended size: 600x400px</p>
                                @if(isset($settings['about_values_image']) && $settings['about_values_image']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['about_values_image']->value) }}"
                                             alt="Values"
                                             class="w-32 h-20 object-cover rounded border">
                                    </div>
                                @endif
                                <input type="file" name="about_values_image" id="about_values_image"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('about_values_image')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Page Images Tab -->
            <div id="contact-images-tab" class="tab-pane hidden">
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">
                        <i class="fas fa-envelope mr-2 text-purple-500"></i>
                        Contact Page Images
                    </h3>
                    <p class="text-gray-600 mb-6">Upload images for your Contact page sections. Images will be automatically optimized and converted to WebP format.</p>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Contact Hero -->
                        <div class="space-y-6">
                            <h4 class="text-md font-semibold text-gray-700 border-b border-gray-300 pb-2">
                                <i class="fas fa-image mr-2 text-purple-500"></i>
                                Hero & Main Images
                            </h4>

                            <!-- Contact Hero Image -->
                            <div class="mb-4">
                                <label for="contact_hero_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Contact Page Hero Image
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Recommended size: 1920x800px</p>
                                @if(isset($settings['contact_hero_image']) && $settings['contact_hero_image']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['contact_hero_image']->value) }}"
                                             alt="Contact Hero"
                                             class="w-32 h-20 object-cover rounded border">
                                    </div>
                                @endif
                                <input type="file" name="contact_hero_image" id="contact_hero_image"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('contact_hero_image')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Office & Location Images -->
                        <div class="space-y-6">
                            <h4 class="text-md font-semibold text-gray-700 border-b border-gray-300 pb-2">
                                <i class="fas fa-building mr-2 text-indigo-500"></i>
                                Office & Location
                            </h4>

                            <!-- Office Image -->
                            <div class="mb-4">
                                <label for="contact_office_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Office Image
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Recommended size: 600x400px</p>
                                @if(isset($settings['contact_office_image']) && $settings['contact_office_image']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['contact_office_image']->value) }}"
                                             alt="Office"
                                             class="w-32 h-20 object-cover rounded border">
                                    </div>
                                @endif
                                <input type="file" name="contact_office_image" id="contact_office_image"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('contact_office_image')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Map/Location Image -->
                            <div class="mb-4">
                                <label for="contact_map_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Location/Map Image
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Recommended size: 800x600px</p>
                                @if(isset($settings['contact_map_image']) && $settings['contact_map_image']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['contact_map_image']->value) }}"
                                             alt="Location Map"
                                             class="w-32 h-20 object-cover rounded border">
                                    </div>
                                @endif
                                <input type="file" name="contact_map_image" id="contact_map_image"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('contact_map_image')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Images Tab -->
            <div id="content-images-tab" class="tab-pane hidden">
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-6">
                        <i class="fas fa-images mr-2 text-red-500"></i>
                        Content Images
                    </h3>
                    <p class="text-gray-600 mb-6">Upload default images for blog posts, testimonials, and other content sections.</p>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Blog Images -->
                        <div class="space-y-6">
                            <h4 class="text-md font-semibold text-gray-700 border-b border-gray-300 pb-2">
                                <i class="fas fa-blog mr-2 text-red-500"></i>
                                Blog Images
                            </h4>

                            <!-- Blog Default Header -->
                            <div class="mb-4">
                                <label for="blog_default_header" class="block text-sm font-medium text-gray-700 mb-2">
                                    Default Blog Header Image
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Used when blog posts don't have a featured image. Recommended size: 1200x600px</p>
                                @if(isset($settings['blog_default_header']) && $settings['blog_default_header']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['blog_default_header']->value) }}"
                                             alt="Blog Default Header"
                                             class="w-32 h-20 object-cover rounded border">
                                    </div>
                                @endif
                                <input type="file" name="blog_default_header" id="blog_default_header"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('blog_default_header')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Testimonial Images -->
                        <div class="space-y-6">
                            <h4 class="text-md font-semibold text-gray-700 border-b border-gray-300 pb-2">
                                <i class="fas fa-quote-left mr-2 text-yellow-500"></i>
                                Testimonial Images
                            </h4>

                            <!-- Testimonials Background -->
                            <div class="mb-4">
                                <label for="testimonials_bg_image" class="block text-sm font-medium text-gray-700 mb-2">
                                    Testimonials Section Background
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Background image for testimonials section. Recommended size: 1920x800px</p>
                                @if(isset($settings['testimonials_bg_image']) && $settings['testimonials_bg_image']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['testimonials_bg_image']->value) }}"
                                             alt="Testimonials Background"
                                             class="w-32 h-20 object-cover rounded border">
                                    </div>
                                @endif
                                <input type="file" name="testimonials_bg_image" id="testimonials_bg_image"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('testimonials_bg_image')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Default Avatar -->
                            <div class="mb-4">
                                <label for="testimonials_default_avatar" class="block text-sm font-medium text-gray-700 mb-2">
                                    Default Avatar Image
                                </label>
                                <p class="text-xs text-gray-500 mb-2">Used when testimonials don't have a profile image. Recommended size: 150x150px</p>
                                @if(isset($settings['testimonials_default_avatar']) && $settings['testimonials_default_avatar']->value)
                                    <div class="mb-2">
                                        <img src="{{ asset('storage/' . $settings['testimonials_default_avatar']->value) }}"
                                             alt="Default Avatar"
                                             class="w-16 h-16 object-cover rounded-full border">
                                    </div>
                                @endif
                                <input type="file" name="testimonials_default_avatar" id="testimonials_default_avatar"
                                       accept="image/*"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-color focus:border-transparent">
                                @error('testimonials_default_avatar')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="mt-8 pt-6 border-t border-gray-200">
            <div class="flex justify-end">
                <button type="submit" class="bg-primary-color hover:bg-secondary-color text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200 flex items-center">
                    <i class="fas fa-save mr-2"></i>
                    Save Settings
                </button>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'border-primary-color', 'text-primary-color');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            tabPanes.forEach(pane => {
                pane.classList.add('hidden');
                pane.classList.remove('active');
            });

            // Add active class to clicked button
            this.classList.add('active', 'border-primary-color', 'text-primary-color');
            this.classList.remove('border-transparent', 'text-gray-500');

            // Show target pane
            const targetPane = document.getElementById(targetTab + '-tab');
            if (targetPane) {
                targetPane.classList.remove('hidden');
                targetPane.classList.add('active');
            }
        });
    });
});
</script>
@endsection
