<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SiteSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add additional page image settings
        $additionalImageSettings = [
            // About page images
            [
                'key' => 'about_hero_image',
                'value' => '',
                'type' => 'image',
                'description' => 'About page hero background image'
            ],
            [
                'key' => 'about_mission_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Mission section image'
            ],
            [
                'key' => 'about_vision_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Vision section image'
            ],
            [
                'key' => 'about_values_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Values section image'
            ],
            [
                'key' => 'about_team_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Team section image'
            ],
            [
                'key' => 'about_facility_tour_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Facility tour section image'
            ],
            // Contact page images
            [
                'key' => 'contact_hero_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Contact page hero background image'
            ],
            [
                'key' => 'contact_office_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Office/building image'
            ],
            [
                'key' => 'contact_map_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Location/map image'
            ],
            // Blog and testimonial images
            [
                'key' => 'blog_default_header',
                'value' => '',
                'type' => 'image',
                'description' => 'Default header image for blog posts without featured images'
            ],
            [
                'key' => 'testimonials_bg_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Background image for testimonials section'
            ],
            [
                'key' => 'testimonials_default_avatar',
                'value' => '',
                'type' => 'image',
                'description' => 'Default avatar for testimonials without profile images'
            ]
        ];

        foreach ($additionalImageSettings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove additional page image settings
        $keys = [
            'about_hero_image',
            'about_mission_image',
            'about_vision_image',
            'about_values_image',
            'about_team_image',
            'about_facility_tour_image',
            'contact_hero_image',
            'contact_office_image',
            'contact_map_image',
            'blog_default_header',
            'testimonials_bg_image',
            'testimonials_default_avatar'
        ];

        SiteSetting::whereIn('key', $keys)->delete();
    }
};
