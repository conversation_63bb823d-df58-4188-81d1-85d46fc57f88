<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use App\Services\ImageService;

class SiteSettingController extends Controller
{
    private $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display site settings form
     */
    public function index()
    {
        $settings = SiteSetting::all()->keyBy('key');
        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update site settings
     */
    public function update(Request $request)
    {
        $request->validate([
            'site_title' => 'required|string|max:255',
            'company_name' => 'required|string|max:255',
            'company_tagline' => 'nullable|string|max:255',
            'currency_symbol' => 'required|string|max:10',
            'currency_code' => 'required|string|max:10',
            'company_phone' => 'nullable|string|max:50',
            'company_email' => 'nullable|email|max:255',
            'company_address' => 'nullable|string',
            'about_us_content' => 'nullable|string',
            'whatsapp_number' => 'nullable|string|max:50',
            'delivery_nationwide' => 'boolean',
            'whatsapp_enabled' => 'boolean',
            'site_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'site_favicon' => 'nullable|image|mimes:jpeg,png,jpg,gif,ico|max:1024',
            'contact_form_emails' => 'nullable|string',
            'quote_request_emails' => 'nullable|string',
            // Home page images
            'hero_slide_1_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:4096',
            'hero_slide_2_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:4096',
            'hero_slide_3_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:4096',
            'company_overview_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'about_section_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'facility_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'team_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            // About page images
            'about_hero_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:4096',
            'about_mission_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'about_vision_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'about_values_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'about_team_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'about_facility_tour_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            // Contact page images
            'contact_hero_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:4096',
            'contact_office_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'contact_map_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            // Blog and testimonial images
            'blog_default_header' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'testimonials_bg_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:4096',
            'testimonials_default_avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:1024',
        ]);

        // Handle text settings
        $textSettings = [
            'site_title', 'company_name', 'company_tagline', 'currency_symbol',
            'currency_code', 'company_phone', 'company_email', 'company_address', 'about_us_content', 'whatsapp_number'
        ];

        foreach ($textSettings as $key) {
            if ($request->has($key)) {
                SiteSetting::set($key, $request->input($key), 'text');
            }
        }

        // Handle boolean settings
        SiteSetting::set('delivery_nationwide', $request->has('delivery_nationwide'), 'boolean');
        SiteSetting::set('whatsapp_enabled', $request->has('whatsapp_enabled'), 'boolean');

        // Handle logo upload
        if ($request->hasFile('site_logo')) {
            $this->handleImageUpload($request->file('site_logo'), 'site_logo', [300, 100]);
        }

        // Handle favicon upload
        if ($request->hasFile('site_favicon')) {
            $faviconPath = $this->imageService->uploadImage($request->file('site_favicon'), 'settings');
            $faviconWebPPath = $this->imageService->convertToWebP($faviconPath);
            SiteSetting::set('site_favicon', $faviconWebPPath, 'image');
        }

        // Handle email settings
        if ($request->has('contact_form_emails')) {
            SiteSetting::set('contact_form_emails', $request->input('contact_form_emails'), 'text');
        }
        if ($request->has('quote_request_emails')) {
            SiteSetting::set('quote_request_emails', $request->input('quote_request_emails'), 'text');
        }

        // Handle all page images
        $pageImages = [
            // Home page images
            'hero_slide_1_image' => [1920, 800],
            'hero_slide_2_image' => [1920, 800],
            'hero_slide_3_image' => [1920, 800],
            'company_overview_image' => [600, 400],
            'about_section_image' => [800, 600],
            'facility_image' => [800, 600],
            'team_image' => [600, 400],
            // About page images
            'about_hero_image' => [1920, 800],
            'about_mission_image' => [600, 400],
            'about_vision_image' => [600, 400],
            'about_values_image' => [600, 400],
            'about_team_image' => [800, 600],
            'about_facility_tour_image' => [800, 600],
            // Contact page images
            'contact_hero_image' => [1920, 800],
            'contact_office_image' => [600, 400],
            'contact_map_image' => [800, 600],
            // Blog and testimonial images
            'blog_default_header' => [1200, 600],
            'testimonials_bg_image' => [1920, 800],
            'testimonials_default_avatar' => [150, 150],
        ];

        foreach ($pageImages as $imageKey => $dimensions) {
            if ($request->hasFile($imageKey)) {
                $this->handlePageImageUpload($request->file($imageKey), $imageKey, $dimensions);
            }
        }

        return redirect()->route('admin.settings.index')
            ->with('success', 'Site settings updated successfully.');
    }

    /**
     * Handle image upload and resizing
     */
    private function handleImageUpload($file, $settingKey, $dimensions = null)
    {
        // Delete old image if exists
        $oldImage = SiteSetting::get($settingKey);
        if ($oldImage && Storage::disk('public')->exists($oldImage)) {
            Storage::disk('public')->delete($oldImage);
        }

        // Store new image
        $path = $file->store('settings', 'public');

        // Resize if dimensions provided
        if ($dimensions) {
            $fullPath = storage_path('app/public/' . $path);
            $manager = new ImageManager(new Driver());
            $image = $manager->read($fullPath);
            $image->resize($dimensions[0], $dimensions[1]);
            $image->save($fullPath);
        }

        SiteSetting::set($settingKey, $path, 'image');
        return $path;
    }

    /**
     * Handle page image upload with WebP conversion
     */
    private function handlePageImageUpload($file, $settingKey, $dimensions = null)
    {
        // Delete old image if exists
        $oldImage = SiteSetting::get($settingKey);
        if ($oldImage && Storage::disk('public')->exists($oldImage)) {
            Storage::disk('public')->delete($oldImage);
        }

        // Determine directory based on image type
        $directory = 'page-images';
        if (str_contains($settingKey, 'hero') || str_contains($settingKey, 'slide')) {
            $directory = 'page-images/heroes';
        } elseif (str_contains($settingKey, 'about')) {
            $directory = 'page-images/about';
        } elseif (str_contains($settingKey, 'contact')) {
            $directory = 'page-images/contact';
        } elseif (str_contains($settingKey, 'blog') || str_contains($settingKey, 'testimonial')) {
            $directory = 'page-images/content';
        }

        // Upload and resize image
        $path = $this->imageService->uploadImage($file, $directory, $dimensions);

        // Convert to WebP for better performance
        $webPPath = $this->imageService->convertToWebP($path);

        // Delete original if WebP conversion was successful
        if ($webPPath !== $path && Storage::disk('public')->exists($path)) {
            Storage::disk('public')->delete($path);
        }

        SiteSetting::set($settingKey, $webPPath, 'image');
        return $webPPath;
    }

}
