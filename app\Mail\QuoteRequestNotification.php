<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class QuoteRequestNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $quoteRequest;

    /**
     * Create a new message instance.
     */
    public function __construct(array $quoteRequest)
    {
        $this->quoteRequest = $quoteRequest;
    }

    /**
     * Build the message.
     */    public function build()
    {
        $companyName = \App\Services\CompanyInfoService::name();
        $inquiryType = isset($this->quoteRequest['inquiry_type']) ? ucfirst(str_replace('_', ' ', $this->quoteRequest['inquiry_type'])) : 'Export Inquiry';

        return $this->subject("New {$inquiryType} - {$companyName}")
            ->markdown('emails.quote-request-notification', ['quoteRequest' => $this->quoteRequest])
            ->from(config('mail.from.address'), $companyName);
    }
}
