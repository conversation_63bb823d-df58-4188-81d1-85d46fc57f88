@extends('layouts.front')

@section('content')
    <!-- Hero Section / Slider -->
    <div class="relative">
        <!-- Slider container -->
        <div class="relative overflow-hidden" id="hero-slider">
            @if($sliders->count() > 0)
                <!-- Dynamic Sliders from Database -->
                @foreach($sliders as $index => $slider)
                    <div class="slide relative h-[700px] bg-cover bg-center {{ $index > 0 ? 'hidden' : '' }}"
                         style="background-image: url('{{ asset('storage/' . $slider->image) }}')">
                        <div class="absolute inset-0 bg-gradient-to-r from-primary-red/30 to-primary-red/20"></div>
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center relative z-10">
                            <div class="max-w-3xl text-white">
                                <div class="flex items-center mb-4">
                                    <div class="bg-primary-gold text-primary-red px-3 py-1 rounded-full text-sm font-semibold mr-4">
                                        <i class="fas fa-certificate mr-1"></i>
                                        Halal Certified
                                    </div>
                                    <div class="bg-success-green text-white px-3 py-1 rounded-full text-sm font-semibold">
                                        <i class="fas fa-globe mr-1"></i>
                                        Export Quality
                                    </div>
                                </div>
                                <h1 class="text-4xl md:text-5xl lg:text-7xl font-bold font-secondary mb-6 leading-tight">{{ $slider->title }}</h1>
                                @if($slider->subtitle)
                                    <p class="text-xl md:text-2xl mb-8 font-medium text-gray-100">{{ $slider->subtitle }}</p>
                                @endif
                                @if($slider->description)
                                    <div class="text-lg mb-8 leading-relaxed text-gray-200">
                                        {!! $slider->description !!}
                                    </div>
                                @endif
                                <div class="flex flex-wrap gap-4">
                                    @if($slider->button_text && $slider->button_link)
                                        <a href="{{ $slider->button_link }}" class="btn-primary text-lg px-8 py-4">
                                            {{ $slider->button_text }}
                                        </a>
                                    @else
                                        <a href="{{ route('products.index') }}" class="btn-primary text-lg px-8 py-4">
                                            <i class="fas fa-drumstick-bite mr-2"></i>
                                            View Our Products
                                        </a>
                                    @endif
                                    <a href="{{ route('contact.index') }}" class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-primary-red px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105">
                                        <i class="fas fa-shipping-fast mr-2"></i>
                                        Export Inquiry
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            @else
                <!-- Static Fallback Sliders -->
                <!-- Slide 1 - Premium Poultry Products -->
                <div class="slide relative h-[700px] bg-cover bg-center"
                     style="background-image: {{ (isset($siteSettings['hero_slide_1_image']) && $siteSettings['hero_slide_1_image'] && file_exists(storage_path('app/public/' . $siteSettings['hero_slide_1_image']))) ? 'url(' . asset('storage/' . $siteSettings['hero_slide_1_image']) . ')' : 'linear-gradient(135deg, #C41E3A 0%, #A01729 100%)' }};">
                    <div class="absolute inset-0 bg-gradient-to-r from-primary-red/30 to-primary-red/20"></div>
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center relative z-10">
                        <div class="max-w-3xl text-white">
                            <div class="flex items-center mb-6">
                                <div class="bg-primary-gold text-primary-red px-4 py-2 rounded-full text-sm font-bold mr-4">
                                    <i class="fas fa-certificate mr-2"></i>
                                    Halal Certified
                                </div>
                                <div class="bg-success-green text-white px-4 py-2 rounded-full text-sm font-bold">
                                    <i class="fas fa-globe mr-2"></i>
                                    Export Quality
                                </div>
                            </div>
                            <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold font-secondary mb-6 leading-tight">Premium Poultry Products from Eswatini</h1>
                            <p class="text-xl md:text-2xl mb-8 font-medium text-gray-100">Supplying high-quality, halal-certified poultry products to domestic and international markets with uncompromising standards.</p>
                            <div class="flex flex-wrap gap-4">
                                <a href="{{ route('products.index') }}" class="btn-primary text-lg px-8 py-4">
                                    <i class="fas fa-drumstick-bite mr-2"></i>
                                    View Our Products
                                </a>
                                <a href="{{ route('contact.index') }}" class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-primary-red px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105">
                                    <i class="fas fa-shipping-fast mr-2"></i>
                                    Export Inquiry
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 2 - Export Excellence -->
                <div class="slide relative h-[700px] bg-cover bg-center hidden"
                     style="background-image: {{ (isset($siteSettings['hero_slide_2_image']) && $siteSettings['hero_slide_2_image'] && file_exists(storage_path('app/public/' . $siteSettings['hero_slide_2_image']))) ? 'url(' . asset('storage/' . $siteSettings['hero_slide_2_image']) . ')' : 'linear-gradient(135deg, #FFD700 0%, #E6C200 100%)' }};">
                    <div class="absolute inset-0 bg-gradient-to-r from-primary-gold/90 to-primary-gold/70"></div>
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center relative z-10">
                        <div class="max-w-3xl text-primary-red">
                            <div class="flex items-center mb-6">
                                <div class="bg-primary-red text-white px-4 py-2 rounded-full text-sm font-bold mr-4">
                                    <i class="fas fa-globe mr-2"></i>
                                    International Export
                                </div>
                                <div class="bg-success-green text-white px-4 py-2 rounded-full text-sm font-bold">
                                    <i class="fas fa-shipping-fast mr-2"></i>
                                    Worldwide Delivery
                                </div>
                            </div>
                            <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold font-secondary mb-6 leading-tight">Exporting Quality to the World</h1>
                            <p class="text-xl md:text-2xl mb-8 font-medium">Serving international markets with premium poultry products, complete documentation, and reliable logistics support.</p>
                            <div class="flex flex-wrap gap-4">
                                <a href="{{ route('contact.index') }}" class="bg-primary-red text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 hover:bg-secondary-red">
                                    <i class="fas fa-envelope mr-2"></i>
                                    Request Quote
                                </a>
                                <a href="{{ route('products.index') }}" class="bg-transparent border-2 border-primary-red text-primary-red hover:bg-primary-red hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105">
                                    <i class="fas fa-list mr-2"></i>
                                    Product Catalog
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 3 - Quality Assurance -->
                <div class="slide relative h-[700px] bg-cover bg-center hidden"
                     style="background-image: {{ (isset($siteSettings['hero_slide_3_image']) && $siteSettings['hero_slide_3_image'] && file_exists(storage_path('app/public/' . $siteSettings['hero_slide_3_image']))) ? 'url(' . asset('storage/' . $siteSettings['hero_slide_3_image']) . ')' : 'linear-gradient(135deg, #27AE60 0%, #229954 100%)' }};">
                    <div class="absolute inset-0 bg-gradient-to-r from-success-green/90 to-success-green/70"></div>
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center relative z-10">
                        <div class="max-w-3xl text-white">
                            <div class="flex items-center mb-6">
                                <div class="bg-white text-success-green px-4 py-2 rounded-full text-sm font-bold mr-4">
                                    <i class="fas fa-shield-alt mr-2"></i>
                                    HACCP Compliant
                                </div>
                                <div class="bg-primary-gold text-primary-red px-4 py-2 rounded-full text-sm font-bold">
                                    <i class="fas fa-award mr-2"></i>
                                    ISO Standards
                                </div>
                            </div>
                            <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold font-secondary mb-6 leading-tight">Uncompromising Quality Standards</h1>
                            <p class="text-xl md:text-2xl mb-8 font-medium text-gray-100">State-of-the-art processing facility with rigorous quality control, food safety protocols, and international certifications.</p>
                            <div class="flex flex-wrap gap-4">
                                <a href="{{ route('about.index') }}" class="bg-white text-success-green px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105 hover:bg-gray-100">
                                    <i class="fas fa-shield-alt mr-2"></i>
                                    Our Quality Standards
                                </a>
                                <a href="{{ route('about.index') }}" class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-success-green px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-300 transform hover:scale-105">
                                    <i class="fas fa-industry mr-2"></i>
                                    Our Facility
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Slider controls -->
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
                <button class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-50 hover:bg-opacity-75 rounded-full w-12 h-12 flex items-center justify-center z-10 focus:outline-none" id="prev-slide">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>
                <button class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-50 hover:bg-opacity-75 rounded-full w-12 h-12 flex items-center justify-center z-10 focus:outline-none" id="next-slide">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>
            </div>

            <!-- Slider indicators -->
            <div class="absolute bottom-5 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
                @if($sliders->count() > 0)
                    @foreach($sliders as $index => $slider)
                        <button class="w-3 h-3 rounded-full bg-white bg-opacity-50 focus:outline-none indicator {{ $index === 0 ? 'active' : '' }}" data-slide="{{ $index }}"></button>
                    @endforeach
                @else
                    <button class="w-3 h-3 rounded-full bg-white bg-opacity-50 focus:outline-none indicator active" data-slide="0"></button>
                    <button class="w-3 h-3 rounded-full bg-white bg-opacity-50 focus:outline-none indicator" data-slide="1"></button>
                    <button class="w-3 h-3 rounded-full bg-white bg-opacity-50 focus:outline-none indicator" data-slide="2"></button>
                    <button class="w-3 h-3 rounded-full bg-white bg-opacity-50 focus:outline-none indicator" data-slide="3"></button>
                @endif
            </div>
        </div>
    </div>

    <!-- Company Overview Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Content -->
                <div>
                    <div class="flex items-center mb-6">
                        <div class="bg-primary-red text-white rounded-full p-3 mr-4">
                            <i class="fas fa-industry text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">About Our Company</h3>
                            <h2 class="text-4xl font-bold font-secondary text-text-dark">Leading Poultry Processor in Eswatini</h2>
                        </div>
                    </div>

                    <p class="text-lg text-gray-600 mb-6 leading-relaxed">
                        {{ $siteSettings['about_us_content'] ?? 'Established as a leading poultry processor in Eswatini, we specialize in producing high-quality, halal-certified poultry products for both domestic and international markets. Our state-of-the-art processing facility ensures the highest standards of food safety and quality.' }}
                    </p>

                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8">
                        <div class="flex items-start">
                            <div class="bg-success-green text-white rounded-full p-2 mr-3 mt-1">
                                <i class="fas fa-certificate text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-text-dark mb-1">Halal Certified</h4>
                                <p class="text-gray-600 text-sm">All products meet Islamic dietary requirements</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="bg-accent-blue text-white rounded-full p-2 mr-3 mt-1">
                                <i class="fas fa-globe text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-text-dark mb-1">Export Quality</h4>
                                <p class="text-gray-600 text-sm">International standards compliance</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="bg-primary-gold text-primary-red rounded-full p-2 mr-3 mt-1">
                                <i class="fas fa-shield-alt text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-text-dark mb-1">HACCP Compliant</h4>
                                <p class="text-gray-600 text-sm">Food safety management system</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="bg-accent-orange text-white rounded-full p-2 mr-3 mt-1">
                                <i class="fas fa-shipping-fast text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-text-dark mb-1">Worldwide Export</h4>
                                <p class="text-gray-600 text-sm">Serving global markets</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-wrap gap-4">
                        <a href="{{ route('about.index') }}" class="btn-primary">
                            <i class="fas fa-info-circle mr-2"></i>
                            Learn More About Us
                        </a>
                        <a href="{{ route('contact.index') }}" class="btn-secondary">
                            <i class="fas fa-envelope mr-2"></i>
                            Contact Us
                        </a>
                    </div>
                </div>

                <!-- Image/Stats -->
                <div class="relative">
                    @if(isset($siteSettings['company_overview_image']) && $siteSettings['company_overview_image'] && file_exists(storage_path('app/public/' . $siteSettings['company_overview_image'])))
                        <!-- Company Image -->
                        <div class="relative rounded-2xl overflow-hidden shadow-2xl">
                            <img src="{{ asset('storage/' . $siteSettings['company_overview_image']) }}"
                                 alt="Company Overview"
                                 class="w-full h-96 object-cover">
                            <div class="absolute inset-0 bg-gradient-to-t from-primary-red/80 to-transparent"></div>

                            <!-- Stats overlay -->
                            <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold font-secondary mb-1">{{ $siteSettings['processing_capacity'] ?? '10,000+' }}</div>
                                        <div class="text-primary-gold text-sm font-medium">Birds Per Day</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold font-secondary mb-1">5+</div>
                                        <div class="text-primary-gold text-sm font-medium">Export Countries</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        <!-- Stats Card (Fallback) -->
                        <div class="bg-gradient-to-br from-primary-red to-secondary-red rounded-2xl p-8 text-white">
                            <div class="grid grid-cols-2 gap-6">
                                <div class="text-center">
                                    <div class="text-4xl font-bold font-secondary mb-2">{{ $siteSettings['processing_capacity'] ?? '10,000+' }}</div>
                                    <div class="text-primary-gold font-medium">Birds Per Day</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-4xl font-bold font-secondary mb-2">5+</div>
                                    <div class="text-primary-gold font-medium">Export Countries</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-4xl font-bold font-secondary mb-2">100%</div>
                                    <div class="text-primary-gold font-medium">Halal Certified</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-4xl font-bold font-secondary mb-2">24/7</div>
                                    <div class="text-primary-gold font-medium">Quality Control</div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Floating certification badges -->
                    <div class="absolute -top-4 -right-4 bg-white rounded-full p-4 shadow-lg">
                        <i class="fas fa-certificate text-3xl text-primary-gold"></i>
                    </div>
                    <div class="absolute -bottom-4 -left-4 bg-white rounded-full p-4 shadow-lg">
                        <i class="fas fa-globe text-3xl text-accent-blue"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products Section with Flip Cards -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <div class="flex items-center justify-center mb-4">
                    <div class="bg-primary-red text-white rounded-full p-3 mr-4">
                        <i class="fas fa-drumstick-bite text-2xl"></i>
                    </div>
                    <div class="text-left">
                        <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">Our Product Range</h3>
                        <h2 class="text-4xl md:text-5xl font-bold font-secondary text-text-dark">
                            {{ $showingFeatured ? 'Featured Poultry Products' : 'Premium Poultry Products' }}
                        </h2>
                    </div>
                </div>
                <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                    {{ $showingFeatured
                        ? 'Discover our premium selection of halal-certified poultry products, processed to international standards and ready for export worldwide.'
                        : 'From whole chickens to specialty cuts, we offer a comprehensive range of high-quality poultry products for domestic and international markets.' }}
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @if($featuredProducts->count() > 0)
                    @foreach($featuredProducts as $product)
                        <!-- Flip Card Container -->
                        <div class="flip-card group min-h-96 perspective-1000">
                            <div class="flip-card-inner relative w-full h-full transition-transform duration-700 transform-style-preserve-3d group-hover:rotate-y-180">

                                <!-- Front of Card -->
                                <div class="flip-card-front absolute inset-0 w-full h-full backface-hidden bg-white rounded-xl shadow-lg overflow-hidden flex flex-col">
                                    <div class="relative h-64">
                                        <img src="{{ asset('storage/' . $product->primary_image) }}"
                                             alt="{{ $product->name }}"
                                             class="w-full h-full object-cover">

                                        <!-- Badges -->
                                        @if($product->hasDiscount())
                                            <div class="absolute top-4 left-4">
                                                <span class="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold animate-pulse">
                                                    @if($product->is_on_sale)
                                                        SALE
                                                    @else
                                                        {{ number_format($product->getCalculatedDiscountPercentage(), 0) }}% OFF
                                                    @endif
                                                </span>
                                            </div>
                                        @endif

                                        @if($product->featured)
                                            <div class="absolute top-4 right-4">
                                                <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                                                    <i class="fas fa-star mr-1"></i>
                                                    Featured
                                                </span>
                                            </div>
                                        @endif

                                        <!-- Hover Indicator -->
                                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                                            <div class="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                                <i class="fas fa-sync-alt text-2xl animate-spin"></i>
                                                <p class="text-sm mt-2">Hover for details</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="p-6 flex-grow flex flex-col">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="text-sm text-primary-color font-medium">{{ $product->category->name ?? 'Uncategorized' }}</span>
                                            <span class="text-xs text-gray-500">{{ $product->created_at->format('M Y') }}</span>
                                        </div>
                                        <h3 class="text-xl font-bold mb-2 text-gray-800 leading-tight">{{ $product->name }}</h3>
                                        <p class="text-gray-600 text-sm flex-grow">{{ Str::limit($product->short_description, 80) }}</p>
                                    </div>
                                </div>

                                <!-- Back of Card -->
                                <div class="flip-card-back absolute inset-0 w-full h-full backface-hidden rotate-y-180 bg-gradient-to-br from-primary-red to-secondary-red rounded-xl shadow-lg overflow-hidden text-white">
                                    <div class="p-6 h-full flex flex-col">
                                        <div class="flex-grow">
                                            <div class="flex items-start mb-4">
                                                <div class="bg-white bg-opacity-20 rounded-full p-2 mr-3 flex-shrink-0">
                                                    <i class="fas fa-drumstick-bite text-xl"></i>
                                                </div>
                                                <div class="flex-grow">
                                                    <h3 class="text-lg font-bold leading-tight mb-1">{{ $product->name }}</h3>
                                                    <p class="text-sm opacity-90">{{ $product->category->name ?? 'Uncategorized' }}</p>
                                                </div>
                                            </div>

                                            <div class="space-y-3 mb-6">
                                                <p class="text-sm leading-relaxed">{{ $product->short_description }}</p>

                                                @if($product->long_description)
                                                    <p class="text-xs opacity-90 leading-relaxed">{{ Str::limit(strip_tags($product->long_description), 120) }}</p>
                                                @endif

                                                <!-- Product Features -->
                                                <div class="flex flex-wrap gap-2 mt-4">
                                                    <span class="bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs">
                                                        <i class="fas fa-certificate mr-1"></i>Halal Certified
                                                    </span>
                                                    <span class="bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs">
                                                        <i class="fas fa-globe mr-1"></i>Export Quality
                                                    </span>
                                                    @if($product->hasDiscount())
                                                        <span class="bg-yellow-400 text-primary-red px-2 py-1 rounded-full text-xs font-bold">
                                                            <i class="fas fa-tag mr-1"></i>Special Offer
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>

                                        <div class="space-y-3 mt-auto">
                                            <button onclick="openHomeQuoteModal({{ $product->id }}, '{{ addslashes($product->name) }}', '{{ addslashes($product->category->name ?? 'Uncategorized') }}', '{{ asset('storage/' . $product->primary_image) }}')"
                                                    class="w-full bg-white text-primary-red px-4 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 hover:shadow-lg">
                                                <i class="fas fa-envelope mr-2"></i>Inquire Now
                                            </button>
                                            <a href="{{ route('products.show', $product->slug) }}"
                                               class="w-full block text-center bg-transparent border-2 border-white text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:bg-white hover:text-primary-red">
                                                <i class="fas fa-eye mr-2"></i>View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <!-- Fallback Static Products -->
                    <!-- Product 1 -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                        <div class="w-full h-64 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-drumstick-bite text-6xl text-primary-red mb-2"></i>
                                <p class="text-gray-500 text-sm">Whole Chicken</p>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-3">
                                <span class="bg-success-green text-white px-2 py-1 rounded-full text-xs font-bold mr-2">
                                    <i class="fas fa-certificate mr-1"></i>Halal
                                </span>
                                <span class="bg-accent-blue text-white px-2 py-1 rounded-full text-xs font-bold">
                                    <i class="fas fa-globe mr-1"></i>Export
                                </span>
                            </div>
                            <h3 class="text-xl font-bold font-secondary text-text-dark mb-2">Premium Whole Chicken</h3>
                            <p class="text-gray-600 mb-4">Fresh, halal-certified whole chickens processed to international standards, perfect for retail and food service.</p>
                            <a href="{{ route('products.index') }}" class="btn-primary text-sm">
                                View Details
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Product 2 -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                        <div class="w-full h-64 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-cut text-6xl text-accent-orange mb-2"></i>
                                <p class="text-gray-500 text-sm">Chicken Parts</p>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-3">
                                <span class="bg-success-green text-white px-2 py-1 rounded-full text-xs font-bold mr-2">
                                    <i class="fas fa-certificate mr-1"></i>Halal
                                </span>
                                <span class="bg-accent-orange text-white px-2 py-1 rounded-full text-xs font-bold">
                                    <i class="fas fa-snowflake mr-1"></i>Frozen
                                </span>
                            </div>
                            <h3 class="text-xl font-bold font-secondary text-text-dark mb-2">Chicken Breast & Thighs</h3>
                            <p class="text-gray-600 mb-4">Premium chicken parts including breasts, thighs, and wings, individually quick frozen for maximum freshness.</p>
                            <a href="{{ route('products.index') }}" class="btn-primary text-sm">
                                View Details
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Product 3 -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                        <div class="w-full h-64 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-star text-6xl text-primary-gold mb-2"></i>
                                <p class="text-gray-500 text-sm">Premium Cuts</p>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center mb-3">
                                <span class="bg-success-green text-white px-2 py-1 rounded-full text-xs font-bold mr-2">
                                    <i class="fas fa-certificate mr-1"></i>Halal
                                </span>
                                <span class="bg-primary-gold text-primary-red px-2 py-1 rounded-full text-xs font-bold">
                                    <i class="fas fa-star mr-1"></i>Premium
                                </span>
                            </div>
                            <h3 class="text-xl font-bold font-secondary text-text-dark mb-2">Specialty Cuts & Fillets</h3>
                            <p class="text-gray-600 mb-4">High-value specialty cuts and boneless fillets, perfect for restaurants and premium retail markets.</p>
                            <a href="{{ route('products.index') }}" class="btn-primary text-sm">
                                View Details
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>
                @endif
            </div>

            <div class="text-center mt-12">
                <a href="{{ route('products.index') }}" class="btn-primary">View All Products</a>
            </div>
        </div>
    </section>

    <!-- About Us Preview Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <div class="flex items-center mb-6">
                        <div class="bg-primary-red text-white rounded-full p-3 mr-4">
                            <i class="fas fa-industry text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">About Our Company</h3>
                            <h2 class="text-3xl md:text-4xl font-bold font-secondary text-text-dark">{{ $siteSettings['company_name'] ?? 'Swazi Poultry Processors Ltd' }}</h2>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6 text-lg leading-relaxed">{{ $siteSettings['about_us_content'] ?? 'Established as a leading poultry processor in Eswatini, we specialize in producing high-quality, halal-certified poultry products for both domestic and international markets. Our state-of-the-art processing facility ensures the highest standards of food safety and quality.' }}</p>
                    <p class="text-gray-600 mb-8 text-lg leading-relaxed">Located in Matsapha Industrial Site, our modern facility operates with cutting-edge technology and adheres to international food safety standards. We have built strong relationships with local farmers and international buyers, creating a sustainable supply chain that benefits our entire community.</p>
                    <div class="flex flex-wrap gap-4">
                        <a href="{{ route('about.index') }}" class="btn-primary">
                            <i class="fas fa-info-circle mr-2"></i>
                            Learn More About Us
                        </a>
                        <a href="{{ route('contact.index') }}" class="btn-secondary">
                            <i class="fas fa-envelope mr-2"></i>
                            Contact Us
                        </a>
                    </div>
                </div>
                <div class="relative">
                    <div class="bg-gradient-to-br from-primary-red to-secondary-red rounded-2xl p-8 text-white">
                        <div class="grid grid-cols-2 gap-6">
                            <div class="text-center">
                                <div class="text-4xl font-bold font-secondary mb-2">{{ $siteSettings['processing_capacity'] ?? '10,000+' }}</div>
                                <div class="text-primary-gold font-medium">Birds Per Day</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold font-secondary mb-2">5+</div>
                                <div class="text-primary-gold font-medium">Export Countries</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold font-secondary mb-2">100%</div>
                                <div class="text-primary-gold font-medium">Halal Certified</div>
                            </div>
                            <div class="text-center">
                                <div class="text-4xl font-bold font-secondary mb-2">24/7</div>
                                <div class="text-primary-gold font-medium">Quality Control</div>
                            </div>
                        </div>
                    </div>

                    <!-- Floating certification badges -->
                    <div class="absolute -top-4 -right-4 bg-white rounded-full p-4 shadow-lg">
                        <i class="fas fa-certificate text-3xl text-primary-gold"></i>
                    </div>
                    <div class="absolute -bottom-4 -left-4 bg-white rounded-full p-4 shadow-lg">
                        <i class="fas fa-globe text-3xl text-accent-blue"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Why Choose Us</h2>
                <p class="text-gray-600 max-w-3xl mx-auto">We pride ourselves on delivering exceptional quality, reliability, and service for all your power and compression needs.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Reason 1 -->
                <div class="bg-white p-8 rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                    <div class="text-success-green text-4xl mb-4">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <h3 class="text-xl font-bold font-secondary text-text-dark mb-3">Halal Certified Excellence</h3>
                    <p class="text-gray-600 leading-relaxed">All our products are certified halal by recognized Islamic authorities, ensuring compliance with religious dietary requirements for global Muslim markets.</p>
                </div>

                <!-- Reason 2 -->
                <div class="bg-white p-8 rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                    <div class="text-accent-blue text-4xl mb-4">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="text-xl font-bold font-secondary text-text-dark mb-3">HACCP Compliance</h3>
                    <p class="text-gray-600 leading-relaxed">Our facility operates under strict HACCP protocols with 24/7 quality monitoring, ensuring the highest food safety standards throughout the processing chain.</p>
                </div>

                <!-- Reason 3 -->
                <div class="bg-white p-8 rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                    <div class="text-accent-orange text-4xl mb-4">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3 class="text-xl font-bold font-secondary text-text-dark mb-3">Export Expertise</h3>
                    <p class="text-gray-600 leading-relaxed">Extensive experience in international trade with complete documentation support, customs clearance assistance, and established logistics networks across multiple continents.</p>
                </div>

                <!-- Reason 4 -->
                <div class="bg-white p-8 rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                    <div class="text-primary-red text-4xl mb-4">
                        <i class="fas fa-snowflake"></i>
                    </div>
                    <h3 class="text-xl font-bold font-secondary text-text-dark mb-3">Cold Chain Excellence</h3>
                    <p class="text-gray-600 leading-relaxed">Advanced refrigeration systems and temperature monitoring ensure product integrity from processing to final delivery, maintaining freshness and quality.</p>
                </div>

                <!-- Reason 5 -->
                <div class="bg-white p-8 rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                    <div class="text-primary-gold text-4xl mb-4">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <h3 class="text-xl font-bold font-secondary text-text-dark mb-3">Reliable Supply Chain</h3>
                    <p class="text-gray-600 leading-relaxed">Consistent production capacity and strong supplier relationships ensure reliable product availability and on-time delivery to meet your market demands.</p>
                </div>

                <!-- Reason 6 -->
                <div class="bg-white p-8 rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                    <div class="text-text-dark text-4xl mb-4">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3 class="text-xl font-bold font-secondary text-text-dark mb-3">Partnership Approach</h3>
                    <p class="text-gray-600 leading-relaxed">We build long-term partnerships with our clients, offering flexible packaging solutions, competitive pricing, and dedicated customer support for sustainable business growth.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Blog Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Latest Articles</h2>
                <p class="text-gray-600 max-w-3xl mx-auto">Stay updated with the latest industry insights, maintenance tips, and product information.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                @if($latestBlogs->count() > 0)
                    @foreach($latestBlogs as $blog)
                        <div class="bg-white rounded-lg shadow-md overflow-hidden transition transform hover:-translate-y-1 hover:shadow-lg">
                            <a href="{{ route('blog.show', $blog->slug) }}">
                                <img src="{{ blog_image($blog->featured_image) }}" alt="{{ $blog->title }}" class="w-full h-48 object-cover">
                            </a>
                            <div class="p-6">
                                <div class="flex items-center text-sm text-gray-500 mb-2">
                                    <i class="far fa-calendar-alt mr-2"></i>
                                    <span>{{ $blog->published_at->format('M j, Y') }}</span>
                                    @if($blog->category)
                                        <span class="mx-2">•</span>
                                        <i class="far fa-folder mr-2"></i>
                                        <span>{{ $blog->category->name }}</span>
                                    @endif
                                </div>
                                <h3 class="text-xl font-bold mb-2">
                                    <a href="{{ route('blog.show', $blog->slug) }}" class="hover:text-primary-color transition-colors">
                                        {{ $blog->title }}
                                    </a>
                                </h3>
                                <p class="text-gray-600 mb-4">{{ $blog->excerpt }}</p>
                                <a href="{{ route('blog.show', $blog->slug) }}" class="text-primary-color hover:text-secondary-color font-medium flex items-center">
                                    Read More
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    @endforeach
                @else
                    <!-- Fallback Static Blog Posts -->
                    <!-- Blog Post 1 -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                        <div class="w-full h-48 bg-gradient-to-br from-success-green to-green-600 flex items-center justify-center">
                            <div class="text-center text-white">
                                <i class="fas fa-certificate text-4xl mb-2"></i>
                                <p class="text-sm font-semibold">Halal Certification</p>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <i class="far fa-calendar-alt mr-2"></i>
                                <span>December 15, 2024</span>
                                <span class="mx-2">•</span>
                                <i class="far fa-folder mr-2"></i>
                                <span>Quality Standards</span>
                            </div>
                            <h3 class="text-xl font-bold font-secondary text-text-dark mb-3">Understanding Halal Certification in Poultry Processing</h3>
                            <p class="text-gray-600 mb-4 leading-relaxed">Learn about the importance of halal certification in poultry processing and how it opens doors to global Muslim markets.</p>
                            <a href="{{ route('blog.index') }}" class="text-primary-red hover:text-secondary-red font-semibold flex items-center">
                                Read More
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Blog Post 2 -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                        <div class="w-full h-48 bg-gradient-to-br from-accent-blue to-blue-600 flex items-center justify-center">
                            <div class="text-center text-white">
                                <i class="fas fa-globe text-4xl mb-2"></i>
                                <p class="text-sm font-semibold">Export Markets</p>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <i class="far fa-calendar-alt mr-2"></i>
                                <span>November 28, 2024</span>
                                <span class="mx-2">•</span>
                                <i class="far fa-folder mr-2"></i>
                                <span>Export Guide</span>
                            </div>
                            <h3 class="text-xl font-bold font-secondary text-text-dark mb-3">Expanding Poultry Exports to African Markets</h3>
                            <p class="text-gray-600 mb-4 leading-relaxed">Discover the growing opportunities for poultry exports across Africa and the requirements for successful market entry.</p>
                            <a href="{{ route('blog.index') }}" class="text-primary-red hover:text-secondary-red font-semibold flex items-center">
                                Read More
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Blog Post 3 -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl">
                        <div class="w-full h-48 bg-gradient-to-br from-accent-orange to-orange-600 flex items-center justify-center">
                            <div class="text-center text-white">
                                <i class="fas fa-snowflake text-4xl mb-2"></i>
                                <p class="text-sm font-semibold">Cold Chain</p>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <i class="far fa-calendar-alt mr-2"></i>
                                <span>October 12, 2024</span>
                                <span class="mx-2">•</span>
                                <i class="far fa-folder mr-2"></i>
                                <span>Technology</span>
                            </div>
                            <h3 class="text-xl font-bold font-secondary text-text-dark mb-3">Cold Chain Management in Poultry Export</h3>
                            <p class="text-gray-600 mb-4 leading-relaxed">Best practices for maintaining product quality through advanced cold chain management from processing to international delivery.</p>
                            <a href="{{ route('blog.index') }}" class="text-primary-red hover:text-secondary-red font-semibold flex items-center">
                                Read More
                                <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                        </div>
                    </div>
                @endif
            </div>

            <div class="text-center mt-10">
                <a href="{{ route('blog.index') }}" class="btn-primary">View All Articles</a>
            </div>
        </div>
    </section>

    <!-- Customer Testimonials Section -->
    @if($testimonials && $testimonials->count() > 0)
        <section class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <div class="flex items-center justify-center mb-6">
                        <div class="bg-primary-red text-white rounded-full p-3 mr-4">
                            <i class="fas fa-comments text-2xl"></i>
                        </div>
                        <div class="text-left">
                            <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">Customer Reviews</h3>
                            <h2 class="text-4xl md:text-5xl font-bold font-secondary text-text-dark">What Our Clients Say</h2>
                        </div>
                    </div>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">Hear from our satisfied customers about their experience with our premium poultry products and services</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    @foreach($testimonials as $testimonial)
                        <div class="bg-gray-50 rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                            <div class="flex items-center mb-6">
                                @if($testimonial->image)
                                    <img src="{{ asset('storage/' . $testimonial->image) }}"
                                         alt="{{ $testimonial->name }}"
                                         class="w-16 h-16 rounded-full object-cover mr-4">
                                @else
                                    <div class="w-16 h-16 rounded-full bg-gradient-to-br from-primary-red to-secondary-red text-white flex items-center justify-center mr-4 text-xl font-bold">
                                        {{ substr($testimonial->name, 0, 1) }}
                                    </div>
                                @endif
                                <div>
                                    <h4 class="font-bold text-text-dark">{{ $testimonial->name }}</h4>
                                    @if($testimonial->position && $testimonial->company)
                                        <p class="text-sm text-gray-600">{{ $testimonial->position }} at {{ $testimonial->company }}</p>
                                    @elseif($testimonial->company)
                                        <p class="text-sm text-gray-600">{{ $testimonial->company }}</p>
                                    @endif
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="flex items-center mb-3">
                                    @for($i = 1; $i <= 5; $i++)
                                        @if($i <= $testimonial->rating)
                                            <i class="fas fa-star text-primary-gold"></i>
                                        @else
                                            <i class="far fa-star text-gray-300"></i>
                                        @endif
                                    @endfor
                                </div>
                                <p class="text-gray-700 italic leading-relaxed">"{{ Str::limit($testimonial->testimonial, 150) }}"</p>
                            </div>

                            <div class="text-xs text-gray-500">
                                {{ $testimonial->created_at->format('M d, Y') }}
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="text-center mt-12">
                    <a href="{{ route('testimonials.index') }}"
                       class="bg-gradient-to-r from-primary-red to-secondary-red hover:from-secondary-red hover:to-primary-red text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        <i class="fas fa-comments mr-2"></i>View All Reviews
                    </a>
                </div>
            </div>
        </section>
    @endif

    <!-- FAQ Section -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <div class="flex items-center justify-center mb-6">
                    <div class="bg-primary-red text-white rounded-full p-3 mr-4">
                        <i class="fas fa-question-circle text-2xl"></i>
                    </div>
                    <div class="text-left">
                        <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">FAQ</h3>
                        <h2 class="text-4xl md:text-5xl font-bold font-secondary text-text-dark">Frequently Asked Questions</h2>
                    </div>
                </div>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">Find answers to common questions about our poultry products, export services, and quality standards.</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
                <!-- Left Column - Company Information -->
                <div class="space-y-8">
                    <div class="bg-white rounded-xl shadow-xl p-8">
                        <h3 class="text-2xl font-bold font-secondary text-text-dark mb-6 flex items-center">
                            <i class="fas fa-info-circle text-primary-red mr-3"></i>
                            About Our Company & Products
                        </h3>

                        <div class="space-y-6 text-gray-600 leading-relaxed">
                            <div>
                                <h4 class="text-lg font-semibold text-text-dark mb-3">Premium Poultry Processing</h4>
                                <p>We specialize in processing premium poultry products at our state-of-the-art facility in Matsapha, Eswatini. Our comprehensive product range includes whole chickens, chicken parts, specialty cuts, and value-added products, all processed to international standards and certified halal for global markets.</p>
                            </div>

                            <div>
                                <h4 class="text-lg font-semibold text-text-dark mb-3">Export Excellence</h4>
                                <p>Our experienced export team provides comprehensive support for international buyers, including complete documentation, customs clearance assistance, and logistics coordination. We understand the complexities of international trade and ensure smooth delivery to markets across Africa, the Middle East, and beyond.</p>
                            </div>

                            <div>
                                <h4 class="text-lg font-semibold text-text-dark mb-3">Quality Assurance</h4>
                                <p>Every product undergoes rigorous quality control processes. Our facility operates under strict HACCP protocols with 24/7 monitoring, ensuring consistent quality and food safety. We maintain detailed traceability records and provide certificates of analysis for all shipments.</p>
                            </div>

                            <div>
                                <h4 class="text-lg font-semibold text-text-dark mb-3">Cold Chain Management</h4>
                                <p>Advanced refrigeration systems and temperature monitoring ensure product integrity from processing to delivery. Our cold chain management maintains optimal temperatures throughout storage and transportation, preserving freshness and extending shelf life for international markets.</p>
                            </div>
                        </div>

                        <div class="mt-8 pt-6 border-t border-gray-200">
                            <div class="flex flex-col sm:flex-row gap-4">
                                <a href="{{ route('products.index') }}" class="btn-primary text-center">
                                    <i class="fas fa-drumstick-bite mr-2"></i>View Our Products
                                </a>
                                <button onclick="openQuoteModal()" class="btn-secondary">
                                    <i class="fas fa-envelope mr-2"></i>Request Quote
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - FAQs -->
                <div class="space-y-8">
                    <div class="bg-white rounded-xl shadow-xl p-8">
                        <h3 class="text-2xl font-bold font-secondary text-text-dark mb-6 flex items-center">
                            <i class="fas fa-question-circle text-primary-red mr-3"></i>
                            Common Questions
                        </h3>

                        <div class="space-y-4">
                            <!-- FAQ Item 1 -->
                            <div class="border-2 border-gray-200 rounded-lg overflow-hidden">
                                <button class="w-full flex justify-between items-center p-4 text-left focus:outline-none hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                                    <span class="text-lg font-semibold text-text-dark">What poultry products do you offer?</span>
                                    <svg class="w-5 h-5 transform transition-transform duration-300 text-primary-red" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="hidden px-4 pb-4 pt-0">
                                    <p class="text-gray-600 leading-relaxed">We offer a comprehensive range of halal-certified poultry products including whole chickens, chicken parts (breasts, thighs, wings, drumsticks), specialty cuts, and value-added products. All products are available fresh or frozen, with flexible packaging options for retail and food service markets.</p>
                                </div>
                            </div>

                            <!-- FAQ Item 2 -->
                            <div class="border-2 border-gray-200 rounded-lg overflow-hidden">
                                <button class="w-full flex justify-between items-center p-4 text-left focus:outline-none hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                                    <span class="text-lg font-semibold text-text-dark">Are all your products halal certified?</span>
                                    <svg class="w-5 h-5 transform transition-transform duration-300 text-primary-red" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="hidden px-4 pb-4 pt-0">
                                    <p class="text-gray-600 leading-relaxed">Yes, 100% of our poultry products are halal certified by recognized Islamic authorities. Our entire processing facility operates under strict halal guidelines, from sourcing to slaughter to packaging. We provide halal certificates with all shipments and maintain detailed documentation for traceability.</p>
                                </div>
                            </div>

                            <!-- FAQ Item 3 -->
                            <div class="border-2 border-gray-200 rounded-lg overflow-hidden">
                                <button class="w-full flex justify-between items-center p-4 text-left focus:outline-none hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                                    <span class="text-lg font-semibold text-text-dark">What export documentation do you provide?</span>
                                    <svg class="w-5 h-5 transform transition-transform duration-300 text-primary-red" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="hidden px-4 pb-4 pt-0">
                                    <p class="text-gray-600 leading-relaxed">We provide complete export documentation including health certificates, halal certificates, certificates of origin, commercial invoices, packing lists, and any country-specific requirements. Our export team handles all customs documentation and works with freight forwarders to ensure smooth clearance at destination ports.</p>
                                </div>
                            </div>

                            <!-- FAQ Item 4 -->
                            <div class="border-2 border-gray-200 rounded-lg overflow-hidden">
                                <button class="w-full flex justify-between items-center p-4 text-left focus:outline-none hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                                    <span class="text-lg font-semibold text-text-dark">What are your minimum order quantities?</span>
                                    <svg class="w-5 h-5 transform transition-transform duration-300 text-primary-red" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="hidden px-4 pb-4 pt-0">
                                    <p class="text-gray-600 leading-relaxed">Minimum order quantities vary by product and destination. For export orders, we typically require a minimum of one 20ft container (approximately 25-27 tons). For domestic orders, smaller quantities can be accommodated. Contact our sales team for specific MOQ requirements based on your product needs and location.</p>
                                </div>
                            </div>

                            <!-- FAQ Item 5 -->
                            <div class="border-2 border-gray-200 rounded-lg overflow-hidden">
                                <button class="w-full flex justify-between items-center p-4 text-left focus:outline-none hover:bg-gray-50 transition-colors" onclick="toggleFAQ(this)">
                                    <span class="text-lg font-semibold text-text-dark">How do you ensure product quality and freshness?</span>
                                    <svg class="w-5 h-5 transform transition-transform duration-300 text-primary-red" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div class="hidden px-4 pb-4 pt-0">
                                    <p class="text-gray-600 leading-relaxed">We maintain strict quality control through HACCP protocols, 24/7 temperature monitoring, and advanced cold chain management. Our products are individually quick frozen (IQF) or blast frozen to lock in freshness. We conduct regular quality tests and provide certificates of analysis with detailed specifications for each shipment.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-20 bg-gradient-to-r from-primary-red to-secondary-red text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="flex items-center justify-center mb-8">
                <div class="bg-primary-gold text-primary-red rounded-full p-4 mr-4">
                    <i class="fas fa-handshake text-3xl"></i>
                </div>
                <div class="text-left">
                    <h3 class="text-primary-gold font-semibold text-sm uppercase tracking-wide">Ready to Partner?</h3>
                    <h2 class="text-4xl md:text-5xl font-bold font-secondary">Let's Discuss Your Needs</h2>
                </div>
            </div>
            <p class="text-xl md:text-2xl mb-10 max-w-4xl mx-auto text-red-100 leading-relaxed">Our export team is ready to help you source premium halal poultry products for your market. From bulk orders to custom packaging, we provide complete solutions for international buyers.</p>
            <div class="flex flex-wrap justify-center gap-6">
                <a href="{{ route('contact.index') }}" class="bg-white text-primary-red hover:bg-gray-100 px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 transform hover:scale-105">
                    <i class="fas fa-phone mr-2"></i>Contact Our Team
                </a>
                <button onclick="openQuoteModal()" class="bg-primary-gold hover:bg-secondary-gold text-primary-red px-8 py-4 rounded-lg font-bold text-lg transition-all duration-300 transform hover:scale-105">
                    <i class="fas fa-envelope mr-2"></i>Request Export Quote
                </button>
            </div>
        </div>
    </section>



    <style>
        /* Flip Card Styles */
        .flip-card {
            perspective: 1000px;
            height: auto;
        }

        .flip-card-inner {
            transform-style: preserve-3d;
            transition: transform 0.7s ease-in-out;
            min-height: 384px; /* Minimum height equivalent to h-96 */
            height: auto;
        }

        .flip-card:hover .flip-card-inner {
            transform: rotateY(180deg);
        }

        .flip-card-front,
        .flip-card-back {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
        }

        .flip-card-back {
            transform: rotateY(180deg);
        }

        .perspective-1000 {
            perspective: 1000px;
        }

        .transform-style-preserve-3d {
            transform-style: preserve-3d;
        }

        .backface-hidden {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
        }

        .rotate-y-180 {
            transform: rotateY(180deg);
        }

        /* Line clamp utility */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Mobile responsiveness for flip cards */
        @media (max-width: 768px) {
            .flip-card:hover .flip-card-inner {
                transform: none; /* Disable flip on mobile hover */
            }

            .flip-card:active .flip-card-inner,
            .flip-card:focus .flip-card-inner {
                transform: rotateY(180deg); /* Enable flip on mobile tap */
            }

            .flip-card {
                cursor: pointer;
            }
        }

        /* Smooth animations */
        .flip-card-inner {
            transition: transform 0.7s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        /* Ensure proper stacking */
        .flip-card-front,
        .flip-card-back {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    </style>

    <script>
        // Home Quote Modal Functionality
        function openHomeQuoteModal(productId, productName, categoryName, productImage) {
            // Update the global quote modal with product information
            const modal = document.getElementById('quote-modal');
            const form = document.getElementById('global-quote-form');

            if (modal && form) {
                // Set product information in the modal
                const productInput = form.querySelector('input[name="product_id"]');
                const messageTextarea = form.querySelector('textarea[name="message"]');

                if (productInput) {
                    productInput.value = productId;
                }

                if (messageTextarea) {
                    messageTextarea.value = `Hi there, Can I get a quote for ${productName}?\n\nI am interested in:\n• Product: ${productName}\n• Category: ${categoryName}\n• Quantity needed: [Please specify]\n• Delivery destination: [Please specify]\n• Timeline: [Please specify]\n\nPlease provide pricing and availability information.`;
                }

                // Update modal title to show product-specific inquiry
                const modalTitle = modal.querySelector('.text-2xl');
                if (modalTitle) {
                    modalTitle.textContent = `Product Inquiry - ${productName}`;
                }

                // Update the subtitle to show it's a product-specific inquiry
                const modalSubtitle = modal.querySelector('.text-primary-gold');
                if (modalSubtitle) {
                    modalSubtitle.textContent = 'Product Inquiry';
                }

                // Pre-select relevant product interest checkbox if it matches
                const checkboxes = form.querySelectorAll('input[name="product_interest[]"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false; // Clear all first
                    if (checkbox.value.toLowerCase().includes(categoryName.toLowerCase()) ||
                        productName.toLowerCase().includes(checkbox.value.toLowerCase())) {
                        checkbox.checked = true;
                    }
                });

                // Show the modal
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        // Mobile Flip Card Functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Handle mobile tap for flip cards
            const flipCards = document.querySelectorAll('.flip-card');

            flipCards.forEach(card => {
                let isFlipped = false;

                card.addEventListener('click', function(e) {
                    // Only handle flip on mobile devices
                    if (window.innerWidth <= 768) {
                        e.preventDefault();

                        const inner = card.querySelector('.flip-card-inner');
                        if (inner) {
                            isFlipped = !isFlipped;
                            if (isFlipped) {
                                inner.style.transform = 'rotateY(180deg)';
                            } else {
                                inner.style.transform = 'rotateY(0deg)';
                            }
                        }
                    }
                });

                // Reset flip state on window resize
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 768) {
                        const inner = card.querySelector('.flip-card-inner');
                        if (inner) {
                            inner.style.transform = '';
                            isFlipped = false;
                        }
                    }
                });
            });
        });

        // Hero Slider Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const slides = document.querySelectorAll('#hero-slider .slide');
            const indicators = document.querySelectorAll('.indicator');
            const prevBtn = document.getElementById('prev-slide');
            const nextBtn = document.getElementById('next-slide');
            let currentSlide = 0;
            let slideInterval = setInterval(nextSlide, 5000);

            function showSlide(index) {
                slides.forEach(slide => slide.classList.add('hidden'));
                indicators.forEach(indicator => indicator.classList.remove('active', 'bg-white', 'bg-opacity-100'));
                indicators.forEach(indicator => indicator.classList.add('bg-white', 'bg-opacity-50'));

                slides[index].classList.remove('hidden');
                indicators[index].classList.add('active', 'bg-white', 'bg-opacity-100');
                currentSlide = index;

                // Reset interval
                clearInterval(slideInterval);
                slideInterval = setInterval(nextSlide, 5000);
            }

            function nextSlide() {
                let next = currentSlide + 1;
                if (next >= slides.length) next = 0;
                showSlide(next);
            }

            function prevSlide() {
                let prev = currentSlide - 1;
                if (prev < 0) prev = slides.length - 1;
                showSlide(prev);
            }

            // Event listeners
            prevBtn.addEventListener('click', prevSlide);
            nextBtn.addEventListener('click', nextSlide);

            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => showSlide(index));
            });

            // Initialize first slide
            showSlide(0);


        });

        // FAQ Toggle Functionality
        function toggleFAQ(element) {
            // Get the content panel
            const content = element.nextElementSibling;

            // Get the arrow icon
            const arrow = element.querySelector('svg');

            // Toggle the content visibility
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                arrow.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                arrow.classList.remove('rotate-180');
            }
        }
    </script>
@endsection
