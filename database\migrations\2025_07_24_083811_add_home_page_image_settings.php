<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\SiteSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add default home page image settings
        $homePageImageSettings = [
            [
                'key' => 'hero_slide_1_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Hero slider image 1 - Premium Products slide'
            ],
            [
                'key' => 'hero_slide_2_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Hero slider image 2 - Export Quality slide'
            ],
            [
                'key' => 'hero_slide_3_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Hero slider image 3 - Quality Standards slide'
            ],
            [
                'key' => 'company_overview_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Company overview section image'
            ],
            [
                'key' => 'about_section_image',
                'value' => '',
                'type' => 'image',
                'description' => 'About section image'
            ],
            [
                'key' => 'facility_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Facility image'
            ],
            [
                'key' => 'team_image',
                'value' => '',
                'type' => 'image',
                'description' => 'Team image'
            ],
            [
                'key' => 'processing_capacity',
                'value' => '10,000+',
                'type' => 'text',
                'description' => 'Daily processing capacity display value'
            ]
        ];

        foreach ($homePageImageSettings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove home page image settings
        $keys = [
            'hero_slide_1_image',
            'hero_slide_2_image',
            'hero_slide_3_image',
            'company_overview_image',
            'about_section_image',
            'facility_image',
            'team_image',
            'processing_capacity'
        ];

        SiteSetting::whereIn('key', $keys)->delete();
    }
};
