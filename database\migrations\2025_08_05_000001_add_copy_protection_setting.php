<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\SiteSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add copy protection setting
        SiteSetting::updateOrCreate(
            ['key' => 'copy_protection_enabled'],
            [
                'key' => 'copy_protection_enabled',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Enable content copy protection features'
            ]
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        SiteSetting::where('key', 'copy_protection_enabled')->delete();
    }
};
