<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\SiteSetting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add copy protection settings
        SiteSetting::updateOrCreate(
            ['key' => 'copy_protection_enabled'],
            [
                'key' => 'copy_protection_enabled',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Enable content copy protection features'
            ]
        );

        SiteSetting::updateOrCreate(
            ['key' => 'copy_protection_watermark'],
            [
                'key' => 'copy_protection_watermark',
                'value' => '',
                'type' => 'text',
                'description' => 'Watermark text for copy protection'
            ]
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        SiteSetting::where('key', 'copy_protection_enabled')->delete();
        SiteSetting::where('key', 'copy_protection_watermark')->delete();
    }
};
